{"__meta": {"id": "01JZAAYF9Y1KYZV2JFXVAVBN08", "datetime": "2025-07-04 09:05:43", "utime": **********.774543, "method": "GET", "uri": "/api/2024-12/organization/billing/status", "ip": "127.0.0.1"}, "php": {"version": "8.2.4", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751619911.342196, "end": **********.774565, "duration": 32.432368993759155, "duration_str": "32.43s", "measures": [{"label": "Booting", "start": 1751619911.342196, "relative_start": 0, "end": **********.736102, "relative_end": **********.736102, "duration": 27.***************, "duration_str": "27.39s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.736118, "relative_start": 27.***************, "end": **********.774567, "relative_end": 1.9073486328125e-06, "duration": 5.***************, "duration_str": "5.04s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.822405, "relative_start": 28.**************, "end": **********.139233, "relative_end": **********.139233, "duration": 0.*****************, "duration_str": "317ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.443676, "relative_start": 32.**************, "end": **********.465275, "relative_end": **********.465275, "duration": 0.*****************, "duration_str": "21.6ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.53161, "relative_start": 32.**************, "end": **********.531759, "relative_end": **********.531759, "duration": 0.00014901161193847656, "duration_str": "149μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "30MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "route": {"uri": "GET api/2024-12/organization/billing/status", "middleware": "api, auth:sanctum", "controller": "App\\Http\\Controllers\\Api\\OrganizationController@billingStatus<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FOrganizationController.php&line=174\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers", "prefix": "api/2024-12", "as": "organization.billing.status", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FOrganizationController.php&line=174\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Api/OrganizationController.php:174-218</a>"}, "queries": {"count": 2, "nb_statements": 2, "nb_visible_statements": 2, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.12107, "accumulated_duration_str": "121ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": null, "name": "vendor/laravel/sanctum/src/Http/Middleware/AuthenticateSession.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\AuthenticateSession.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.97722, "duration": 0.12036, "duration_str": "120ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 0, "width_percent": 99.414}, {"sql": "select * from `organizations` where `id` is null and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 177}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.14419, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "OrganizationController.php:177", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 177}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FOrganizationController.php&line=177", "ajax": false, "filename": "OrganizationController.php", "line": "177"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 99.414, "width_percent": 0.586}]}, "models": {"data": {"App\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "ikPoMer4OtoDULeT9sBt2cHa7xhxWBWDS3oAnx7H", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "PHPDEBUGBAR_STACK_DATA": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/api/2024-12/organization/billing/status\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "password_hash_web": "null"}, "request": {"data": {"status": "404 Not Found", "full_url": "http://localhost:8000/api/2024-12/organization/billing/status", "action_name": "organization.billing.status", "controller_action": "App\\Http\\Controllers\\Api\\OrganizationController@billingStatus", "uri": "GET api/2024-12/organization/billing/status", "controller": "App\\Http\\Controllers\\Api\\OrganizationController@billingStatus<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FOrganizationController.php&line=174\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers", "prefix": "api/2024-12", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FOrganizationController.php&line=174\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Api/OrganizationController.php:174-218</a>", "middleware": "api", "telescope": "<a href=\"http://localhost:8000/_debugbar/telescope/9f4f1476-8d2c-400e-8708-3838ce8a7cda\" target=\"_blank\">View in Telescope</a>", "duration": "32.52s", "peak_memory": "32MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1143933797 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1143933797\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-787000561 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-787000561\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1311324296 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6InA5aVh2V2lvZHFZejVVS0JFMjIxOEE9PSIsInZhbHVlIjoiTzBtYzhBQWpXZy9COEZDb1Vjd0RmdjRDLzhSWU1JMEhWS092bnloR2NyN3BFZVNNVWZ1ZVViVkJZZ0xYbmNJWHFtVG53SWlXY0Zwd1FKelpaQUZHL2JPQTV1OGVnRnBBNUNrZ01ubU1FRzFLcndtcWY0Ym44K1hZTGVIVlZMODEiLCJtYWMiOiIyZjA3MjExYTZhN2M2ZWExZjEyZDY2YjQ0M2Y1MTVkZTk4YmZmN2Y4NTI4YzBiZDQwZWVhM2Y1YzdkZjExMmQ0IiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">http://localhost:8000/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1205 characters\">__stripe_mid=5fa877f3-5d7b-4255-b2c2-ced2f9ae1950465a52; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IkVRWTIyWSs4OCs4d2QyKy9Bc2xMWVE9PSIsInZhbHVlIjoiU1VHWEZSZ21ERUxuOWhrODBFd1FHblFHam1JMmhLckpTTVU4bFBqTlgyVTUzYXlBaGhRU0R4RlBrc2Q0YWE3d0tpQUhhWGNaRG1yRTZRM2tNRFIyY0dLWkxjT0tPME9GMjQyU2lnck1CTEJ2OTV4NlE1cXBMMUVid1J5VzYvenBIUXV1Y2NLL1krZno4WkM4ZnFSaXl3PT0iLCJtYWMiOiJhZGU2ZmY5MzgwYTQ4MmVhMmQ1ODg2MDY2NTMyYjc4NmMyNDUwN2Y1ZmYyZGZmZmExZmI2ZWZhNzdkMWY4M2FiIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InA5aVh2V2lvZHFZejVVS0JFMjIxOEE9PSIsInZhbHVlIjoiTzBtYzhBQWpXZy9COEZDb1Vjd0RmdjRDLzhSWU1JMEhWS092bnloR2NyN3BFZVNNVWZ1ZVViVkJZZ0xYbmNJWHFtVG53SWlXY0Zwd1FKelpaQUZHL2JPQTV1OGVnRnBBNUNrZ01ubU1FRzFLcndtcWY0Ym44K1hZTGVIVlZMODEiLCJtYWMiOiIyZjA3MjExYTZhN2M2ZWExZjEyZDY2YjQ0M2Y1MTVkZTk4YmZmN2Y4NTI4YzBiZDQwZWVhM2Y1YzdkZjExMmQ0IiwidGFnIjoiIn0%3D; apimio_local_session=eyJpdiI6ImE3TG80RlVHREQwQ3pEVWNUN01jeUE9PSIsInZhbHVlIjoicGNXSjZ5VE9aS1IzYmNsOC9tOTZ1TFBUNE80d0Q0RnZJcEtjUnM2SklqL01xc2xmbVgwYlBCeDBvRVdrNktsNXZOaVU1K3d3SGYvUkg1US9sYVc2S212SlNhVkJ1bVB6emFqSU5mbWNhY1pUM3FMZUlpZkVHN3hNY2hvemZTZjYiLCJtYWMiOiJjMjU2YTU4Yzc2N2Q1ZmVhMjg0MGMwMTdjNTJmY2RhMmQ2ZDM0NzE2ODdhN2MwMmQ0NWNhNjU5ODlhZTA2NWJmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1311324296\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1970786570 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"63 characters\">1|w18UHrcfC6aDKtbZiXNufqtJTAKIroQoSW6TxcJOAUYCh5QhrhhnAegAY6Pz|</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ikPoMer4OtoDULeT9sBt2cHa7xhxWBWDS3oAnx7H</span>\"\n  \"<span class=sf-dump-key>apimio_local_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G6vUaCGaiiVUPPlE5EqRfguRyuMqBd2l0mN5HMUb</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1970786570\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1437937543 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 04 Jul 2025 09:05:43 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-limit</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">60</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-remaining</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">59</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InhNRm5OMGN0U013cENZWU42K2RsSnc9PSIsInZhbHVlIjoiY2VSVHdmNEF5Q0IySkFDVzI5bXJQUWpDRmR1d3VDN3lVSHhZVTFlNkYzQkRtRkJtWXU5aWNEQ05rY3h3dzZvTmJOU2w3WHV2YnlWT2w3cEtoWStod1ROR2Z2OWMwVGNKNStLek5WUnlhQmF0VWthdzhnaXAxbzdwaCsrbHdCNUgiLCJtYWMiOiJkN2I4N2E1MGZlOTc4ZmJlODg5Y2RiMzljZWExMzI5YTY5OWUzY2VkMDg5ODVmNGYzMDQ1ZTdlZGEzYTg2YjE2IiwidGFnIjoiIn0%3D; expires=Fri, 04 Jul 2025 11:05:43 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"448 characters\">apimio_local_session=eyJpdiI6ImlzNE10emRybDZnRTRUZmwwSTA0S2c9PSIsInZhbHVlIjoiVTlPZU9yV1FJUXBrMnNMWDI3STYya3B5WktJSlRCNDd6Nlh3Q1llT0FyQm9PdFV0aW4wTXFQZFkxQWVBTVlMQTJ2d2cvY0tJRGJXNnpVYmRpZHJscXdUZTd1ZGhDSVpObFBMMnRBSmJmdzBCQzVqWllZa21VMTc5OUViTjJMdVMiLCJtYWMiOiIxMDVkYjkwMTI3NmVjZDc5NTQzZjQ2ODE0Zjc3OWVjOGFjNTM3NTY2MjIzNWQ2YzIxZWIxYWE5MzEyYjc2N2M2IiwidGFnIjoiIn0%3D; expires=Fri, 04 Jul 2025 11:05:43 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InhNRm5OMGN0U013cENZWU42K2RsSnc9PSIsInZhbHVlIjoiY2VSVHdmNEF5Q0IySkFDVzI5bXJQUWpDRmR1d3VDN3lVSHhZVTFlNkYzQkRtRkJtWXU5aWNEQ05rY3h3dzZvTmJOU2w3WHV2YnlWT2w3cEtoWStod1ROR2Z2OWMwVGNKNStLek5WUnlhQmF0VWthdzhnaXAxbzdwaCsrbHdCNUgiLCJtYWMiOiJkN2I4N2E1MGZlOTc4ZmJlODg5Y2RiMzljZWExMzI5YTY5OWUzY2VkMDg5ODVmNGYzMDQ1ZTdlZGEzYTg2YjE2IiwidGFnIjoiIn0%3D; expires=Fri, 04-Jul-2025 11:05:43 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"420 characters\">apimio_local_session=eyJpdiI6ImlzNE10emRybDZnRTRUZmwwSTA0S2c9PSIsInZhbHVlIjoiVTlPZU9yV1FJUXBrMnNMWDI3STYya3B5WktJSlRCNDd6Nlh3Q1llT0FyQm9PdFV0aW4wTXFQZFkxQWVBTVlMQTJ2d2cvY0tJRGJXNnpVYmRpZHJscXdUZTd1ZGhDSVpObFBMMnRBSmJmdzBCQzVqWllZa21VMTc5OUViTjJMdVMiLCJtYWMiOiIxMDVkYjkwMTI3NmVjZDc5NTQzZjQ2ODE0Zjc3OWVjOGFjNTM3NTY2MjIzNWQ2YzIxZWIxYWE5MzEyYjc2N2M2IiwidGFnIjoiIn0%3D; expires=Fri, 04-Jul-2025 11:05:43 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1437937543\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1591512152 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ikPoMer4OtoDULeT9sBt2cHa7xhxWBWDS3oAnx7H</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"61 characters\">http://localhost:8000/api/2024-12/organization/billing/status</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>password_hash_web</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1591512152\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "404 Not Found", "full_url": "http://localhost:8000/api/2024-12/organization/billing/status", "action_name": "organization.billing.status", "controller_action": "App\\Http\\Controllers\\Api\\OrganizationController@billingStatus"}, "badge": "404 Not Found"}}