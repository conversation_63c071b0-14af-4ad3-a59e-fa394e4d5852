{"__meta": {"id": "01JZAB3SRVDZ2QXMP29CY71AJ1", "datetime": "2025-07-04 09:08:38", "utime": **********.300986, "method": "GET", "uri": "/api/2024-12/organization", "ip": "127.0.0.1"}, "php": {"version": "8.2.4", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751620117.487851, "end": **********.301017, "duration": 0.8131661415100098, "duration_str": "813ms", "measures": [{"label": "Booting", "start": 1751620117.487851, "relative_start": 0, "end": **********.160102, "relative_end": **********.160102, "duration": 0.****************, "duration_str": "672ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.160129, "relative_start": 0.****************, "end": **********.30102, "relative_end": 2.86102294921875e-06, "duration": 0.*****************, "duration_str": "141ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.182393, "relative_start": 0.****************, "end": **********.189954, "relative_end": **********.189954, "duration": 0.0075609683990478516, "duration_str": "7.56ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.289559, "relative_start": 0.****************, "end": **********.289855, "relative_end": **********.289855, "duration": 0.0002961158752441406, "duration_str": "296μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.292828, "relative_start": 0.****************, "end": **********.293031, "relative_end": **********.293031, "duration": 0.0002028942108154297, "duration_str": "203μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "30MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "route": {"uri": "GET api/2024-12/organization", "middleware": "api, auth:sanctum", "as": "organization.index", "controller": "App\\Http\\Controllers\\Api\\OrganizationController@index<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FOrganizationController.php&line=28\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers", "prefix": "api/2024-12", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FOrganizationController.php&line=28\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Api/OrganizationController.php:28-39</a>"}, "queries": {"count": 2, "nb_statements": 2, "nb_visible_statements": 2, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00617, "accumulated_duration_str": "6.17ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": null, "name": "vendor/laravel/sanctum/src/Http/Middleware/AuthenticateSession.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\AuthenticateSession.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.216251, "duration": 0.00359, "duration_str": "3.59ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 0, "width_percent": 58.185}, {"sql": "select `organizations`.*, `organization_user`.`user_id` as `pivot_user_id`, `organization_user`.`organization_id` as `pivot_organization_id` from `organizations` inner join `organization_user` on `organizations`.`id` = `organization_user`.`organization_id` where `organization_user`.`user_id` = 1 and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) order by `updated_at` desc", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 37}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.267453, "duration": 0.0025800000000000003, "duration_str": "2.58ms", "memory": 0, "memory_str": null, "filename": "OrganizationController.php:37", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FOrganizationController.php&line=37", "ajax": false, "filename": "OrganizationController.php", "line": "37"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 58.185, "width_percent": 41.815}]}, "models": {"data": {"App\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "ikPoMer4OtoDULeT9sBt2cHa7xhxWBWDS3oAnx7H", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/api/2024-12/organization\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "password_hash_web": "null", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/organization/select\"\n]"}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/api/2024-12/organization", "action_name": "organization.index", "controller_action": "App\\Http\\Controllers\\Api\\OrganizationController@index", "uri": "GET api/2024-12/organization", "controller": "App\\Http\\Controllers\\Api\\OrganizationController@index<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FOrganizationController.php&line=28\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers", "prefix": "api/2024-12", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FOrganizationController.php&line=28\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Api/OrganizationController.php:28-39</a>", "middleware": "api", "telescope": "<a href=\"http://localhost:8000/_debugbar/telescope/9f4f157e-5415-4e78-b442-729c002705d8\" target=\"_blank\">View in Telescope</a>", "duration": "818ms", "peak_memory": "32MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-562529369 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-562529369\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1982199077 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1982199077\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1197321057 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">Bearer null******</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Inl4NStUd0gxZU1LQW1kc2NjeFNoYlE9PSIsInZhbHVlIjoiUFlYYWx4eHlHdFU0eGNoQ2JaUThqcmZ3Q1lackJSbGl2ZDBwZlRJRjROcE5WRXNZcURHWDUxTjFSQmFUTU1XV1Q2Z1BBZTRHWUlGbkgvWVFpS0g2VktCSENIQ1YrU3JDZSt3VG9tREpzT2dhbCtZYUUvSTBXOWM1K010d3AzS3oiLCJtYWMiOiJkZWZmZTRlOGQyZmM2ZDY2OTI5ZTY2ZWVlMWY1NDEyOWU2ZDE4MmQ0ZDMwOGYwMGQzOWVkNTRhZThmNWEzYTBiIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost:8000/organization</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1205 characters\">__stripe_mid=5fa877f3-5d7b-4255-b2c2-ced2f9ae1950465a52; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Ikh6c1lPTHd2QXUrT2F0OWs5NU5nT3c9PSIsInZhbHVlIjoiWFphNTcvZERRSjNkS3FwV3RTRkxFcUV6VHJNZDA4eTdNanREMFNQYXZsc1YyMFFTdEFNUVBKTldJaVlKK3dyZTdyYzN0cisvZldoMmNPenFnbXByTDVlaEI3VXlKa2E1VzBHbzZKNU9IOU00Tzg0ZzBvOHhBM0d6MlRSZEdEcUZTK3Y2M3NsRjJ4ZEVuY1VzQmlpalBnPT0iLCJtYWMiOiI2OWM5ZmM3MDEyNTI4YTE2N2VhMjcxYzI0NmE5YjU2Zjg1MWE5ZjcxZDE4ZTFlNWYyZGE5ZjUyYzQ4NmQ5M2ZmIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Inl4NStUd0gxZU1LQW1kc2NjeFNoYlE9PSIsInZhbHVlIjoiUFlYYWx4eHlHdFU0eGNoQ2JaUThqcmZ3Q1lackJSbGl2ZDBwZlRJRjROcE5WRXNZcURHWDUxTjFSQmFUTU1XV1Q2Z1BBZTRHWUlGbkgvWVFpS0g2VktCSENIQ1YrU3JDZSt3VG9tREpzT2dhbCtZYUUvSTBXOWM1K010d3AzS3oiLCJtYWMiOiJkZWZmZTRlOGQyZmM2ZDY2OTI5ZTY2ZWVlMWY1NDEyOWU2ZDE4MmQ0ZDMwOGYwMGQzOWVkNTRhZThmNWEzYTBiIiwidGFnIjoiIn0%3D; apimio_local_session=eyJpdiI6Im1qTk1aM05QYW5JamE2RTVGbG9iK1E9PSIsInZhbHVlIjoiTGt2R3RlNks0bzBBZXBmSWRPdk9GTFpnS21aR2MwOFJPd1UyQjNWU09NWFFEcjlPNURaZXNMQjNiRWtOSGV3aCswTHRjY1M4ME5SVTZxWnBaczVsVENyanRMWGdEcmwxbnJybmFrNVpUTUFseURnTmxhcDZQT2ZoQ3JVVGViL1kiLCJtYWMiOiJiMGFmMjAwYzlhNjgwMDU3N2NmZDQzMGY1YTA5OTZjN2U2Y2QxN2ZhNjdmMzhmM2ZmNDRiYTgxZmEwMmNlMzJjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1197321057\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-460674455 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"63 characters\">1|QRtdjvk3r2KD6Sq63FZBSWVCBmzsXTPvhwN1XrFHsg0VmpaG2uGCyLItysDk|</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ikPoMer4OtoDULeT9sBt2cHa7xhxWBWDS3oAnx7H</span>\"\n  \"<span class=sf-dump-key>apimio_local_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SpQkFOIEOJLpae86rFzEvQ4jqZkdfqtcgZefgT1H</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-460674455\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1475195987 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 04 Jul 2025 09:08:38 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-limit</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">60</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-remaining</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">59</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Ii94VHhqdS91NTNlalRYVGFuMUQ0T0E9PSIsInZhbHVlIjoiT2g5RHNFNGZNWnRXK3g2VkhjUFc0UFZLQmV4VXB2TllxSHJYZHUvQ2h5OUxsajViUGNDYUREbFZsNWhoREg1bzBramxESkVQTTQyL2cvQ3B2REJBVllobnlqZVJRQkk0bHVsTEszUjJMWkZOczBDV1lTM2p4NGJHeDB3ZW5qWW8iLCJtYWMiOiI0YzYyNGJjOTc5MTg1MTFkMjZkODRiZGFiYmU1YzU0YTJmNGUwZDljZDUzODcxODc3MWE4ZDRhNjMwYzY0M2ZiIiwidGFnIjoiIn0%3D; expires=Fri, 04 Jul 2025 11:08:38 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"448 characters\">apimio_local_session=eyJpdiI6IjhHVEIzWjdMN1NnTXRLWUcrcnJEbXc9PSIsInZhbHVlIjoiK1F3UHhoM3BndmMxZUNtT3c1R0QxR0JpKy9wbTZGeEQyamgwd0QwTUhxWGZYbDhZN0ZzM0JDOG4rbmlvQUVjTkpUVURteDFJd25PU0E1a0cvNDArOE1wSjZKYSt5ZEZwR3NtWmJFWlRvOGpOYnF5RGZKa1U2ZXk4V1BLYW1xbFgiLCJtYWMiOiJmNzZjNWY4OWE2NWQzMWFiMDI4NTMxNDRkNDVkMmVhMzM5MWQ0Zjk4YzQ1YTExZjgyN2RhNGY4YTI2MDViYmU0IiwidGFnIjoiIn0%3D; expires=Fri, 04 Jul 2025 11:08:38 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Ii94VHhqdS91NTNlalRYVGFuMUQ0T0E9PSIsInZhbHVlIjoiT2g5RHNFNGZNWnRXK3g2VkhjUFc0UFZLQmV4VXB2TllxSHJYZHUvQ2h5OUxsajViUGNDYUREbFZsNWhoREg1bzBramxESkVQTTQyL2cvQ3B2REJBVllobnlqZVJRQkk0bHVsTEszUjJMWkZOczBDV1lTM2p4NGJHeDB3ZW5qWW8iLCJtYWMiOiI0YzYyNGJjOTc5MTg1MTFkMjZkODRiZGFiYmU1YzU0YTJmNGUwZDljZDUzODcxODc3MWE4ZDRhNjMwYzY0M2ZiIiwidGFnIjoiIn0%3D; expires=Fri, 04-Jul-2025 11:08:38 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"420 characters\">apimio_local_session=eyJpdiI6IjhHVEIzWjdMN1NnTXRLWUcrcnJEbXc9PSIsInZhbHVlIjoiK1F3UHhoM3BndmMxZUNtT3c1R0QxR0JpKy9wbTZGeEQyamgwd0QwTUhxWGZYbDhZN0ZzM0JDOG4rbmlvQUVjTkpUVURteDFJd25PU0E1a0cvNDArOE1wSjZKYSt5ZEZwR3NtWmJFWlRvOGpOYnF5RGZKa1U2ZXk4V1BLYW1xbFgiLCJtYWMiOiJmNzZjNWY4OWE2NWQzMWFiMDI4NTMxNDRkNDVkMmVhMzM5MWQ0Zjk4YzQ1YTExZjgyN2RhNGY4YTI2MDViYmU0IiwidGFnIjoiIn0%3D; expires=Fri, 04-Jul-2025 11:08:38 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1475195987\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-137625624 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ikPoMer4OtoDULeT9sBt2cHa7xhxWBWDS3oAnx7H</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"46 characters\">http://localhost:8000/api/2024-12/organization</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>password_hash_web</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"41 characters\">http://localhost:8000/organization/select</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-137625624\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/api/2024-12/organization", "action_name": "organization.index", "controller_action": "App\\Http\\Controllers\\Api\\OrganizationController@index"}, "badge": null}}