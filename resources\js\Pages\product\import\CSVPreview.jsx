import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON>po<PERSON>, <PERSON>ton, Table, Alert, Row, Col, Radio } from "antd";
import { CheckCircleOutlined, UploadOutlined, SyncOutlined } from "@ant-design/icons";
import SVG1 from "../../../../../public/v2/icons/importNew.svg";
import SVG2 from "../../../../../public/v2/icons/importnewandexisting.svg";
import SVG3 from "../../../../../public/v2/icons/importupdate.svg";
import ProductsLayout from "../ProductsLayout";
// import CSVMapping from "../../../../../packages/apimio/mapping-fields-package/resources/js/pages/CSVMapping";

const { Title, Text } = Typography;
const { Group } = Radio;

const CSVPreview = ({ csvData, onReupload, onNext, hasExistingProducts = false }) => {
    // Set initial state based on whether products exist
    const [importAction, setImportAction] = useState(hasExistingProducts ? 3 : null);

    const handleImportActionChange = (e) => {
        setImportAction(e.target.value);
    };

    // Update import action when hasExistingProducts changes
    useEffect(() => {
        if (hasExistingProducts && importAction === null) {
            setImportAction(3); // Default to "Import New Products Only" when products exist
        } else if (!hasExistingProducts) {
            setImportAction(null); // Clear selection when no products exist
        }
    }, [hasExistingProducts]);

    const analyzeCSV = () => {
        const headers = csvData.headers;
        // Find duplicate columns
        const columnCounts = headers.reduce((acc, header) => {
            acc[header] = (acc[header] || 0) + 1;
            return acc;
        }, {});

        const duplicates = Object.entries(columnCounts)
            .filter(([_, count]) => count > 1)
            .map(([header, count]) => ({ header, count }));

        return {
            rowCount: csvData.totalRows,
            columnCount: headers.length,
            duplicateColumns: duplicates,
        };
    };

    const fileAnalysis = analyzeCSV();

    return (
        <ProductsLayout activeTab="importcsv">
            <>
        <div className="h-full p-0 m-0">
            <Row className="pb-[20px]">
                <Col span={12}>
                    <div>
                        <p className="text-[#252525] font-[600] text-[18px]">Import CSV</p>
                        <p className="text-[#626262] font-normal text-[14px]">Import CSV of all your products</p>
                    </div>
                </Col>
                <Col span={12}>
                    <div className="flex justify-end gap-[8px]">
                        <Button icon={<SyncOutlined />} onClick={onReupload}>
                            Reupload File
                        </Button>
                        <Button
                            className="bg-[#740898] text-white border border-[#740898] rounded-[4px]"
                            onClick={() => onNext(importAction)}
                        >
                            Next
                        </Button>
                    </div>
                </Col>
            </Row>
            {/* Conditionally render import action cards only when products exist */}
            {hasExistingProducts && (
                <Row className="pb-[20px]">
                    <div className="flex justify-between items-center gap-2 w-full">
                        <Radio.Group onChange={handleImportActionChange} value={importAction} className="w-full flex justify-between gap-2">
                            <Card className={`relative bg-white ${importAction === 3 ? 'border-purple-500' : 'border-purple-100'} hover:border-purple-200 transition-colors w-1/3`} size="small">
                                {/* Absolutely positioned radio in the top-right corner */}
                                <Radio
                                    className="absolute top-1 right-1 text-[#252525] text-[16px]"
                                    value={3} // Import New Products Only = 3
                                />

                                {/* Main content */}
                                <div className="flex justify-between items-center space-x-4 p-3">
                                    <div className="p-4 bg-[#F1E6F5] rounded-full">
                                        <img src={SVG1} alt="Spreadsheet Icon" />
                                    </div>
                                    <div>
                                        <Text strong className="block mb-1">
                                            Import New Products Only
                                        </Text>
                                        <Text className="text-xs text-gray-600">
                                            This option will ignore existing SKUs and will import new SKUs only.
                                        </Text>
                                    </div>
                                </div>
                            </Card>
                            <Card className={`relative bg-white ${importAction === 2 ? 'border-purple-500' : 'border-purple-100'} hover:border-purple-200 transition-colors w-1/3`} size="small">
                                {/* Absolutely positioned radio in the top-right corner */}
                                <Radio
                                    className="absolute top-1 right-1 text-[#252525] text-[16px]"
                                    value={2} // Update Existing Products = 2
                                />

                                {/* Main content */}
                                <div className="flex justify-between items-center space-x-4 p-3">
                                    <div className="p-4 bg-[#F1E6F5] rounded-full">
                                        <img src={SVG3} alt="Spreadsheet Icon" />
                                    </div>
                                    <div>
                                        <Text strong className="block mb-1">
                                            Update Existing Products
                                        </Text>
                                        <Text className="text-xs text-gray-600">
                                            This option will update only existing SKUs and ignore new SKUs only.
                                        </Text>
                                    </div>
                                </div>
                            </Card>
                            <Card className={`relative bg-white ${importAction === 1 ? 'border-purple-500' : 'border-purple-100'} hover:border-purple-200 transition-colors w-1/3`} size="small">
                                {/* Absolutely positioned radio in the top-right corner */}
                                <Radio
                                    className="absolute top-1 right-1 text-[#252525] text-[16px]"
                                    value={1} // New and Update Existing = 1
                                />

                                {/* Main content */}
                                <div className="flex justify-between items-center space-x-4 p-3">
                                    <div className="p-4 bg-[#F1E6F5] rounded-full">
                                        <img src={SVG2} alt="Spreadsheet Icon" />
                                    </div>
                                    <div>
                                        <Text strong className="block mb-1">
                                            New and Update Existing
                                        </Text>
                                        <Text className="text-xs text-gray-600">
                                            This option will update existing SKUs and will create new SKUs.
                                        </Text>
                                    </div>
                                </div>
                            </Card>
                        </Radio.Group>
                    </div>
                </Row>
            )}
            {/* {fileAnalysis.duplicateColumns.length > 0 && (
                <Alert
                    className="mt-4"
                    type="warning"
                    message="Duplicate Columns Detected"
                    description={
                        <div>
                            The following columns appear multiple times:
                            <ul className="list-disc ml-4 mt-2">
                                {fileAnalysis.duplicateColumns.map(({ header, count }) => (
                                    <li key={header}>
                                        "{header}" appears {count} times
                                    </li>
                                ))}
                            </ul>
                        </div>
                    }
                />
            )} */}

            <div className={` bg-white  rounded-[12px] border border-[#DBDBDB]   pb-0 mb-0`}>
                <Row>
                    <Col span={12}>
                        <p className="text-[#252525] font-[600] text-[18px] p-5">CSV Preview</p>
                    </Col>
                    <Col span={12}>
                        <div className="flex justify-end gap-[8px] p-5">
                            <div className="flex items-center space-x-2">
                                <CheckCircleOutlined className="text-green-500" />
                                <Text>{fileAnalysis.columnCount} Columns Found</Text>
                            </div>
                            <div className="flex items-center space-x-2">
                                <CheckCircleOutlined className="text-green-500" />
                                <Text>{fileAnalysis.rowCount} Rows Found</Text>
                            </div>
                            <div className="flex items-center space-x-2">
                                <CheckCircleOutlined className="text-green-500" />
                                <Text>
                                    {fileAnalysis.duplicateColumns.length === 0
                                        ? "No Duplicate Columns"
                                        : `${fileAnalysis.duplicateColumns.length} Duplicate Columns Found`}
                                </Text>
                            </div>
                        </div>
                    </Col>
                </Row>
                <div className="overflow-x-auto">
                    <Table
                        dataSource={csvData.rows}
                        columns={csvData.headers.map((header) => ({
                            title: header,
                            dataIndex: header,
                            key: header,
                            ellipsis: true,
                        }))}
                        size="small"
                        pagination={false}
                        scroll={{ x: true }}
                    />
                </div>
                <Text className="block mt-4 text-gray-500">Showing 5 preview rows out of {csvData.totalRows} total rows</Text>
            </div>
        </div>
                </>
        </ProductsLayout>

    );
};

export default CSVPreview;
