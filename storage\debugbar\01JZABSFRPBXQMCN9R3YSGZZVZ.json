{"__meta": {"id": "01JZABSFRPBXQMCN9R3YSGZZVZ", "datetime": "2025-07-04 09:20:28", "utime": **********.951323, "method": "GET", "uri": "/api/2024-12/shopify-sync-status", "ip": "127.0.0.1"}, "php": {"version": "8.2.4", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[09:20:28] LOG.warning: Optional parameter $id declared before required parameter $version_id is implicitly treated as a required parameter in C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Channel\\ShopifyChannel.php on line 716", "message_html": null, "is_string": false, "label": "warning", "time": **********.928732, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.242321, "end": **********.951357, "duration": 0.7090358734130859, "duration_str": "709ms", "measures": [{"label": "Booting", "start": **********.242321, "relative_start": 0, "end": **********.828308, "relative_end": **********.828308, "duration": 0.****************, "duration_str": "586ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.828321, "relative_start": 0.****************, "end": **********.95136, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "123ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.845569, "relative_start": 0.***************, "end": **********.85104, "relative_end": **********.85104, "duration": 0.005470991134643555, "duration_str": "5.47ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.943492, "relative_start": 0.****************, "end": **********.944034, "relative_end": **********.944034, "duration": 0.0005421638488769531, "duration_str": "542μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.947154, "relative_start": 0.****************, "end": **********.947274, "relative_end": **********.947274, "duration": 0.00011992454528808594, "duration_str": "120μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "30MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "route": {"uri": "GET api/2024-12/shopify-sync-status", "middleware": "api, auth:sanctum", "controller": "App\\Http\\Controllers\\Api\\DashboardController@shopifySyncStatus<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FDashboardController.php&line=185\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers", "prefix": "api/2024-12", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FDashboardController.php&line=185\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Api/DashboardController.php:185-222</a>"}, "queries": {"count": 2, "nb_statements": 2, "nb_visible_statements": 2, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.02478, "accumulated_duration_str": "24.78ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": null, "name": "vendor/laravel/sanctum/src/Http/Middleware/AuthenticateSession.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\AuthenticateSession.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.878277, "duration": 0.02404, "duration_str": "24.04ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 0, "width_percent": 97.014}, {"sql": "select * from `channels` where `organization_id` = 1 and exists (select * from `shopify_channels` where `channels`.`id` = `shopify_channels`.`channel_id`)", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Api/DashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\DashboardController.php", "line": 189}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.933157, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:189", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Api/DashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\DashboardController.php", "line": 189}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FDashboardController.php&line=189", "ajax": false, "filename": "DashboardController.php", "line": "189"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 97.014, "width_percent": 2.986}]}, "models": {"data": {"App\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "ikPoMer4OtoDULeT9sBt2cHa7xhxWBWDS3oAnx7H", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/api/2024-12/shopify-sync-status\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "password_hash_web": "null", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/organization/select\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]", "organization_id": "1"}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/api/2024-12/shopify-sync-status", "action_name": null, "controller_action": "App\\Http\\Controllers\\Api\\DashboardController@shopifySyncStatus", "uri": "GET api/2024-12/shopify-sync-status", "controller": "App\\Http\\Controllers\\Api\\DashboardController@shopifySyncStatus<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FDashboardController.php&line=185\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers", "prefix": "api/2024-12", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FDashboardController.php&line=185\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Api/DashboardController.php:185-222</a>", "middleware": "api", "telescope": "<a href=\"http://localhost:8000/_debugbar/telescope/9f4f19ba-b00d-4f28-982b-e9ddb9111445\" target=\"_blank\">View in Telescope</a>", "duration": "715ms", "peak_memory": "32MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-812825381 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-812825381\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-561364510 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-561364510\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2132884263 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">Bearer null******</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImxmRVY0dWNvQk9jU1E0RGYwNk11ZFE9PSIsInZhbHVlIjoiNVpPaytRdWp5eG9DQ2lqbElsVTh3c0t6Tmd2OWZSaWx0Mm1FbDhoNFBTU1JwSkRXOWw4RzV1cjhOcjhudEI0NWlGTUJnSjBlOXQ4ZzVYdnluWEhZenF0UHdWOGlOOEp1a29vWml2Q0hwTzJLN0NBZ3F1K2pId3N5S3lrZmFJdm8iLCJtYWMiOiI2NjgyZjg2YjkwMzgwYWM4OTE3MGI4Zjk1MzcwZjVjOWZlNGIzYjlkNmQ2YjFmM2MxMjVkMzI3OTdkODFiYzUzIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">http://localhost:8000/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1205 characters\">__stripe_mid=5fa877f3-5d7b-4255-b2c2-ced2f9ae1950465a52; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Ikh6c1lPTHd2QXUrT2F0OWs5NU5nT3c9PSIsInZhbHVlIjoiWFphNTcvZERRSjNkS3FwV3RTRkxFcUV6VHJNZDA4eTdNanREMFNQYXZsc1YyMFFTdEFNUVBKTldJaVlKK3dyZTdyYzN0cisvZldoMmNPenFnbXByTDVlaEI3VXlKa2E1VzBHbzZKNU9IOU00Tzg0ZzBvOHhBM0d6MlRSZEdEcUZTK3Y2M3NsRjJ4ZEVuY1VzQmlpalBnPT0iLCJtYWMiOiI2OWM5ZmM3MDEyNTI4YTE2N2VhMjcxYzI0NmE5YjU2Zjg1MWE5ZjcxZDE4ZTFlNWYyZGE5ZjUyYzQ4NmQ5M2ZmIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImxmRVY0dWNvQk9jU1E0RGYwNk11ZFE9PSIsInZhbHVlIjoiNVpPaytRdWp5eG9DQ2lqbElsVTh3c0t6Tmd2OWZSaWx0Mm1FbDhoNFBTU1JwSkRXOWw4RzV1cjhOcjhudEI0NWlGTUJnSjBlOXQ4ZzVYdnluWEhZenF0UHdWOGlOOEp1a29vWml2Q0hwTzJLN0NBZ3F1K2pId3N5S3lrZmFJdm8iLCJtYWMiOiI2NjgyZjg2YjkwMzgwYWM4OTE3MGI4Zjk1MzcwZjVjOWZlNGIzYjlkNmQ2YjFmM2MxMjVkMzI3OTdkODFiYzUzIiwidGFnIjoiIn0%3D; apimio_local_session=eyJpdiI6IjZaUEhrR3cwQjVxOWFHdlBHUFJiRkE9PSIsInZhbHVlIjoiNi9YbElTRUdKdWszZ1JGZEYvakhiSzlJVFJLSUdqb1BBK051VVUxcS90ZmUzbjEzQS9QdHRvc3ljOUlGVjdQWXQ4OU43ak5sYWVoMjJPdjZhUm1HSjdUbFp6MnNnUTl0SnFQVnZrWXl5eWZvblRzQmt4TDFjT2hrRENnZnI2UWUiLCJtYWMiOiI0YjhjMTBmY2JiMGY1OGZkYjM3ZmZkMjc2YzZhM2MxOTNjNTEyZGMwZTY4MzBkOGUxY2M1NGNhZjdlYTNjM2UwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2132884263\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1361916002 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"63 characters\">1|QRtdjvk3r2KD6Sq63FZBSWVCBmzsXTPvhwN1XrFHsg0VmpaG2uGCyLItysDk|</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ikPoMer4OtoDULeT9sBt2cHa7xhxWBWDS3oAnx7H</span>\"\n  \"<span class=sf-dump-key>apimio_local_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SpQkFOIEOJLpae86rFzEvQ4jqZkdfqtcgZefgT1H</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1361916002\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1083501523 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 04 Jul 2025 09:20:28 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-limit</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">60</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-remaining</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">56</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IkhaRXdYNUlIZjdOSmpDYkNyR1I1ZUE9PSIsInZhbHVlIjoiR1NVNU8zQVRpVlh2STJMVjlsajlYY2hUMVV2MnFKTVhQeXhTRy9NMHdkck40b0hidXNwdE82VGFJR3lNcFJIQnJEdW9RTEdpc3E1QW1wditLNHhoTjhRSDBYazF0aitZa3pBR1VteGZaZXZDeU1heDhpYUs0NjRSTEhUTUhkeDIiLCJtYWMiOiIwZDI3Y2Y3NDViNWQyMjQyMWJiOTUxYjNiOGVhYzkxY2QwYzU4MjhjNWJmOWI2NTVlODFlMjllMTk5M2I5ODc5IiwidGFnIjoiIn0%3D; expires=Fri, 04 Jul 2025 11:20:28 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"448 characters\">apimio_local_session=eyJpdiI6InVoRUFpdW5iSEk1R2M4YmlKSE4yQVE9PSIsInZhbHVlIjoiVVJtQ1hqbndLQzZKVVhDY0oyZms2MWNpOWw2Ky9iUVJYZUVsTjdKZnVBaHFwb1hyd3pRcGsxQXpvbEZFQ1FIS3NwWDdwT1dwK2NDK2RKMXVrcjZTUDhqZ3RxcG1QWjF6TU9YY3NNeFlpTHNaTVlNVUk4b1V1WWJna2JzRUJJU08iLCJtYWMiOiJmYzBiNmI4NDVkYjg2ZjgxMDgzY2U1ZGQwN2I4ZmIwNzI4MzZkZDFiZTJhMmQ5NDQzYmMyZmVlZDRiMDQzMmI0IiwidGFnIjoiIn0%3D; expires=Fri, 04 Jul 2025 11:20:28 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkhaRXdYNUlIZjdOSmpDYkNyR1I1ZUE9PSIsInZhbHVlIjoiR1NVNU8zQVRpVlh2STJMVjlsajlYY2hUMVV2MnFKTVhQeXhTRy9NMHdkck40b0hidXNwdE82VGFJR3lNcFJIQnJEdW9RTEdpc3E1QW1wditLNHhoTjhRSDBYazF0aitZa3pBR1VteGZaZXZDeU1heDhpYUs0NjRSTEhUTUhkeDIiLCJtYWMiOiIwZDI3Y2Y3NDViNWQyMjQyMWJiOTUxYjNiOGVhYzkxY2QwYzU4MjhjNWJmOWI2NTVlODFlMjllMTk5M2I5ODc5IiwidGFnIjoiIn0%3D; expires=Fri, 04-Jul-2025 11:20:28 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"420 characters\">apimio_local_session=eyJpdiI6InVoRUFpdW5iSEk1R2M4YmlKSE4yQVE9PSIsInZhbHVlIjoiVVJtQ1hqbndLQzZKVVhDY0oyZms2MWNpOWw2Ky9iUVJYZUVsTjdKZnVBaHFwb1hyd3pRcGsxQXpvbEZFQ1FIS3NwWDdwT1dwK2NDK2RKMXVrcjZTUDhqZ3RxcG1QWjF6TU9YY3NNeFlpTHNaTVlNVUk4b1V1WWJna2JzRUJJU08iLCJtYWMiOiJmYzBiNmI4NDVkYjg2ZjgxMDgzY2U1ZGQwN2I4ZmIwNzI4MzZkZDFiZTJhMmQ5NDQzYmMyZmVlZDRiMDQzMmI0IiwidGFnIjoiIn0%3D; expires=Fri, 04-Jul-2025 11:20:28 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1083501523\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1728751473 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ikPoMer4OtoDULeT9sBt2cHa7xhxWBWDS3oAnx7H</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"53 characters\">http://localhost:8000/api/2024-12/shopify-sync-status</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>password_hash_web</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"41 characters\">http://localhost:8000/organization/select</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>organization_id</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1728751473\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/api/2024-12/shopify-sync-status", "controller_action": "App\\Http\\Controllers\\Api\\DashboardController@shopifySyncStatus"}, "badge": null}}