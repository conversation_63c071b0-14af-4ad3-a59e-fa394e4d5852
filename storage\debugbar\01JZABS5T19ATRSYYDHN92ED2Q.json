{"__meta": {"id": "01JZABS5T19ATRSYYDHN92ED2Q", "datetime": "2025-07-04 09:20:18", "utime": **********.755504, "method": "GET", "uri": "/api/2024-12/invite-permissions", "ip": "127.0.0.1"}, "php": {"version": "8.2.4", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751620817.511137, "end": **********.755532, "duration": 1.2443950176239014, "duration_str": "1.24s", "measures": [{"label": "Booting", "start": 1751620817.511137, "relative_start": 0, "end": **********.545848, "relative_end": **********.545848, "duration": 1.****************, "duration_str": "1.03s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.545878, "relative_start": 1.***************, "end": **********.755535, "relative_end": 2.86102294921875e-06, "duration": 0.****************, "duration_str": "210ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.565579, "relative_start": 1.****************, "end": **********.571795, "relative_end": **********.571795, "duration": 0.0062160491943359375, "duration_str": "6.22ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.745887, "relative_start": 1.****************, "end": **********.746173, "relative_end": **********.746173, "duration": 0.00028586387634277344, "duration_str": "286μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.748731, "relative_start": 1.****************, "end": **********.748876, "relative_end": **********.748876, "duration": 0.00014519691467285156, "duration_str": "145μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "29MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "route": {"uri": "GET api/2024-12/invite-permissions", "middleware": "api, auth:sanctum", "controller": "App\\Http\\Controllers\\Api\\Invite\\TeamInviteController@permissions<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FInvite%2FTeamInviteController.php&line=152\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers", "prefix": "api/2024-12", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FInvite%2FTeamInviteController.php&line=152\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Api/Invite/TeamInviteController.php:152-159</a>"}, "queries": {"count": 2, "nb_statements": 2, "nb_visible_statements": 2, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.02702, "accumulated_duration_str": "27.02ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": null, "name": "vendor/laravel/sanctum/src/Http/Middleware/AuthenticateSession.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\AuthenticateSession.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.654592, "duration": 0.025589999999999998, "duration_str": "25.59ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 0, "width_percent": 94.708}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/Invite/TeamInviteController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\Invite\\TeamInviteController.php", "line": 154}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.7288, "duration": 0.0014299999999999998, "duration_str": "1.43ms", "memory": 0, "memory_str": null, "filename": "TeamInviteController.php:154", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/Invite/TeamInviteController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\Invite\\TeamInviteController.php", "line": 154}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FInvite%2FTeamInviteController.php&line=154", "ajax": false, "filename": "TeamInviteController.php", "line": "154"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 94.708, "width_percent": 5.292}]}, "models": {"data": {"App\\Models\\Organization\\Permission": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 6, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "ikPoMer4OtoDULeT9sBt2cHa7xhxWBWDS3oAnx7H", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/api/2024-12/invite-permissions\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "password_hash_web": "null", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/organization/select\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]", "organization_id": "1"}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/api/2024-12/invite-permissions", "action_name": null, "controller_action": "App\\Http\\Controllers\\Api\\Invite\\TeamInviteController@permissions", "uri": "GET api/2024-12/invite-permissions", "controller": "App\\Http\\Controllers\\Api\\Invite\\TeamInviteController@permissions<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FInvite%2FTeamInviteController.php&line=152\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers", "prefix": "api/2024-12", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FInvite%2FTeamInviteController.php&line=152\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Api/Invite/TeamInviteController.php:152-159</a>", "middleware": "api", "telescope": "<a href=\"http://localhost:8000/_debugbar/telescope/9f4f19ab-2288-499f-acc7-18f0c284a911\" target=\"_blank\">View in Telescope</a>", "duration": "1.25s", "peak_memory": "30MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-2119259854 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2119259854\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-224414281 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-224414281\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">Bearer null******</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6InJSdjB6aG9YYWVXUDBHeHBPdFYyTXc9PSIsInZhbHVlIjoiZ0llRkRnSmNwU3JqUFVRQS9aWmREMktueVVabzRZbzZ4L3pTd2MrRTlTb2ZsMi94empwK2svSCtRdm56VmU3OVZDVXQyVzVhZ1N0Z05Jdy9YOC9DcllmdFpzeXV2dCt5N2JOZzdPUXVqU2pMNGRVZ3ZJUldsVEUyTjhud1ZjYnQiLCJtYWMiOiJlYjcyMGFjYmRjZDYxNWMwM2JhNTg1ZDgxNTBkNGRiZDdhMjY5ZmNkMTk0MGRmZTU3ZmVlMGQ5ODk4N2NlNjYxIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">http://localhost:8000/onboarding</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1205 characters\">__stripe_mid=5fa877f3-5d7b-4255-b2c2-ced2f9ae1950465a52; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Ikh6c1lPTHd2QXUrT2F0OWs5NU5nT3c9PSIsInZhbHVlIjoiWFphNTcvZERRSjNkS3FwV3RTRkxFcUV6VHJNZDA4eTdNanREMFNQYXZsc1YyMFFTdEFNUVBKTldJaVlKK3dyZTdyYzN0cisvZldoMmNPenFnbXByTDVlaEI3VXlKa2E1VzBHbzZKNU9IOU00Tzg0ZzBvOHhBM0d6MlRSZEdEcUZTK3Y2M3NsRjJ4ZEVuY1VzQmlpalBnPT0iLCJtYWMiOiI2OWM5ZmM3MDEyNTI4YTE2N2VhMjcxYzI0NmE5YjU2Zjg1MWE5ZjcxZDE4ZTFlNWYyZGE5ZjUyYzQ4NmQ5M2ZmIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InJSdjB6aG9YYWVXUDBHeHBPdFYyTXc9PSIsInZhbHVlIjoiZ0llRkRnSmNwU3JqUFVRQS9aWmREMktueVVabzRZbzZ4L3pTd2MrRTlTb2ZsMi94empwK2svSCtRdm56VmU3OVZDVXQyVzVhZ1N0Z05Jdy9YOC9DcllmdFpzeXV2dCt5N2JOZzdPUXVqU2pMNGRVZ3ZJUldsVEUyTjhud1ZjYnQiLCJtYWMiOiJlYjcyMGFjYmRjZDYxNWMwM2JhNTg1ZDgxNTBkNGRiZDdhMjY5ZmNkMTk0MGRmZTU3ZmVlMGQ5ODk4N2NlNjYxIiwidGFnIjoiIn0%3D; apimio_local_session=eyJpdiI6IlJ6UnJsclhmM3U4cm9PaEJSWFJTRUE9PSIsInZhbHVlIjoieDdSMHlZbk1QV3ZoS0gzYTd1SWkrWnRoaXFRajZKMkJ2ckxEeUR4Y3krcGVodXhrZllyMU42dk5Ydy9tQjkvbGlBRktVdTNqcXJKRWRCRUtkWE0rMy9mNWJMamorWDdmUlVXK2x3ZDlxeDlOamhxWmtEaGdubkhTcEhLYnBMUTciLCJtYWMiOiI2ZDVhMjYwNDU4ZDYwZWJkMTc1NmIxZTFjOGQxYWIwOGU5M2RjYjI2YThhNDkxMWQ5MmYwOTUxYjU2M2JmOTVhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-533481124 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"63 characters\">1|QRtdjvk3r2KD6Sq63FZBSWVCBmzsXTPvhwN1XrFHsg0VmpaG2uGCyLItysDk|</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ikPoMer4OtoDULeT9sBt2cHa7xhxWBWDS3oAnx7H</span>\"\n  \"<span class=sf-dump-key>apimio_local_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SpQkFOIEOJLpae86rFzEvQ4jqZkdfqtcgZefgT1H</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-533481124\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-918371593 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 04 Jul 2025 09:20:18 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-limit</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">60</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-remaining</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">59</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InNiMzk1b09rMVViV0lEM1VML0pQWHc9PSIsInZhbHVlIjoiN2hEOHA0VEZlK3VvN0c4K3VNMnhHQzdIaE9tamk0czJ2a0R5dmhHYWNIekxEMnRFZ0tVOFhwV21hY0YrSGJDVGpNOGR3Ry9EbU94eFlnL2Qrck5JcWozSFR5cHJjQzNMZUR0SU16UVlGakYwQ0lIQ0hPeU9MOFRZSkl2N0ZHRkIiLCJtYWMiOiJkZTI0ZjYyYTM0MjgwMmE3NmU4ODBjOWNlZjYyYTRkZTA3MTVlNzI0ZTM4OTJmYjY1YzM1ODU0MjYwMmYzYjY4IiwidGFnIjoiIn0%3D; expires=Fri, 04 Jul 2025 11:20:18 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"448 characters\">apimio_local_session=eyJpdiI6IjVEb1hXcXBScU5pNDVVcXFuQVAyaUE9PSIsInZhbHVlIjoid0FvZDJxNDNDTmVEOTZWcklHK2p6NFhFNDZJQThrYUhab0p0QU52MVh4TUJtZWV1WDV1enJtQTVyRGUzREUxckloSGpFeUNIQ095VjRZeVVxckNiTllJVXhhaHNYMHg3TlhjaGJEeHNlUkZORFhrV1ViRHhvTVpLeHovR0VRZngiLCJtYWMiOiIxZmZjNjIyNDVjOTIyOTVjMWFlOTIxODlmYzJlMmVjYjBlNDE4N2JmODQxNGJmNDRkNmQ2OWQyMDNlZmJkNzFiIiwidGFnIjoiIn0%3D; expires=Fri, 04 Jul 2025 11:20:18 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InNiMzk1b09rMVViV0lEM1VML0pQWHc9PSIsInZhbHVlIjoiN2hEOHA0VEZlK3VvN0c4K3VNMnhHQzdIaE9tamk0czJ2a0R5dmhHYWNIekxEMnRFZ0tVOFhwV21hY0YrSGJDVGpNOGR3Ry9EbU94eFlnL2Qrck5JcWozSFR5cHJjQzNMZUR0SU16UVlGakYwQ0lIQ0hPeU9MOFRZSkl2N0ZHRkIiLCJtYWMiOiJkZTI0ZjYyYTM0MjgwMmE3NmU4ODBjOWNlZjYyYTRkZTA3MTVlNzI0ZTM4OTJmYjY1YzM1ODU0MjYwMmYzYjY4IiwidGFnIjoiIn0%3D; expires=Fri, 04-Jul-2025 11:20:18 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"420 characters\">apimio_local_session=eyJpdiI6IjVEb1hXcXBScU5pNDVVcXFuQVAyaUE9PSIsInZhbHVlIjoid0FvZDJxNDNDTmVEOTZWcklHK2p6NFhFNDZJQThrYUhab0p0QU52MVh4TUJtZWV1WDV1enJtQTVyRGUzREUxckloSGpFeUNIQ095VjRZeVVxckNiTllJVXhhaHNYMHg3TlhjaGJEeHNlUkZORFhrV1ViRHhvTVpLeHovR0VRZngiLCJtYWMiOiIxZmZjNjIyNDVjOTIyOTVjMWFlOTIxODlmYzJlMmVjYjBlNDE4N2JmODQxNGJmNDRkNmQ2OWQyMDNlZmJkNzFiIiwidGFnIjoiIn0%3D; expires=Fri, 04-Jul-2025 11:20:18 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-918371593\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2071649711 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ikPoMer4OtoDULeT9sBt2cHa7xhxWBWDS3oAnx7H</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"52 characters\">http://localhost:8000/api/2024-12/invite-permissions</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>password_hash_web</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"41 characters\">http://localhost:8000/organization/select</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>organization_id</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2071649711\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/api/2024-12/invite-permissions", "controller_action": "App\\Http\\Controllers\\Api\\Invite\\TeamInviteController@permissions"}, "badge": null}}