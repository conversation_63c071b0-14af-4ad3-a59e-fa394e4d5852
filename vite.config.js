import { defineConfig } from "vite";
import laravel from "laravel-vite-plugin";
import react from "@vitejs/plugin-react";
import tailwindcss from '@tailwindcss/vite'

// Define the path based on the Laravel environment
const mappingComponentPath =
    process.env.APP_ENV === "local"
        ? "packages/apimio/mapping-fields-package/resources/js/components/MappingModuleRedux.jsx"
        : "vendor/apimio/mapping-connector-package/resources/js/components/MappingModuleRedux.jsx";

export default defineConfig({
    plugins: [
        tailwindcss(),
        laravel({
            input: [
                "resources/css/globals.css",
                "resources/js/app.jsx",
                "resources/js/Pages/BulkEdit.jsx",
                "resources/js/Pages/auth/ManualLinking.jsx",
                "resources/js/components/productListing/ListingTable.jsx",
                "resources/js/components/productListing/Filters.jsx",
                "resources/js/components/productListing/FilterMappng.jsx",
                "resources/js/components/brandportal/BrandPortal.jsx",
                "resources/js/components/productVariants/index.jsx",
                "resources/js/components/productVariants/novariants.jsx",
                "resources/js/components/productVariants/step-1.jsx",
                "resources/js/components/Test.jsx",
                "resources/js/components/productListing/BrandsPortalFilters.jsx",
                "resources/js/components/productListing/BrandPortalFilterMapping.jsx",
                "resources/js/components/productListing/ListingTableForBrandPortal.jsx",
                "resources/js/components/brandportal/PublicView.jsx",
                "resources/js/components/brandportal/BrandPortal.jsx",
                mappingComponentPath,
            ],
            refresh: true,
        }),
        react(),
    ],
    server: {
        host: '0.0.0.0',
        cors: {
            origin: '*',
            methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
            allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
        },
        hmr: {
            host: 'localhost'
        }
    }
});
