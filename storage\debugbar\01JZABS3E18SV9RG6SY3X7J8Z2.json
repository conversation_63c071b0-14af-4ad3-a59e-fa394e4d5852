{"__meta": {"id": "01JZABS3E18SV9RG6SY3X7J8Z2", "datetime": "2025-07-04 09:20:16", "utime": **********.3233, "method": "POST", "uri": "/api/2024-12/organization", "ip": "127.0.0.1"}, "php": {"version": "8.2.4", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[09:20:00] LOG.warning: Optional parameter $invite_channel_ids declared before required parameter $authuser is implicitly treated as a required parameter in C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Invite\\Invite.php on line 372", "message_html": null, "is_string": false, "label": "warning", "time": **********.653676, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1751620781.881759, "end": **********.323339, "duration": 34.441580057144165, "duration_str": "34.44s", "measures": [{"label": "Booting", "start": 1751620781.881759, "relative_start": 0, "end": **********.385583, "relative_end": **********.385583, "duration": 3.**************, "duration_str": "3.5s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.385602, "relative_start": 3.***************, "end": **********.323343, "relative_end": 4.0531158447265625e-06, "duration": 30.**************, "duration_str": "30.94s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.493513, "relative_start": 3.****************, "end": **********.501703, "relative_end": **********.501703, "duration": 0.008189916610717773, "duration_str": "8.19ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.316732, "relative_start": 34.*************, "end": **********.316965, "relative_end": **********.316965, "duration": 0.00023317337036132812, "duration_str": "233μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.31911, "relative_start": 34.**************, "end": **********.31925, "relative_end": **********.31925, "duration": 0.00014019012451171875, "duration_str": "140μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "37MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "route": {"uri": "POST api/2024-12/organization", "middleware": "api, auth:sanctum", "as": "organization.store", "controller": "App\\Http\\Controllers\\Api\\OrganizationController@store<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FOrganizationController.php&line=67\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers", "prefix": "api/2024-12", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FOrganizationController.php&line=67\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Api/OrganizationController.php:67-111</a>"}, "queries": {"count": 58, "nb_statements": 56, "nb_visible_statements": 58, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 8.410109999999998, "accumulated_duration_str": "8.41s", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": null, "name": "vendor/laravel/sanctum/src/Http/Middleware/AuthenticateSession.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\AuthenticateSession.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.580476, "duration": 0.12182, "duration_str": "122ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 0, "width_percent": 1.448}, {"sql": "select count(*) as aggregate from `organizations` where `name` = 'Tanzayb' and (`id` = 1)", "type": "query", "params": [], "bindings": ["<PERSON><PERSON><PERSON><PERSON>", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 948}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 660}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 459}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 494}], "start": 1751620790.644171, "duration": 0.22568000000000002, "duration_str": "226ms", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 1.448, "width_percent": 2.683}, {"sql": "select count(*) as aggregate from `organizations` where `name` = 'Tanzayb' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1)", "type": "query", "params": [], "bindings": ["<PERSON><PERSON><PERSON><PERSON>", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Rules/UniqueManyToMany.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Rules\\UniqueManyToMany.php", "line": 55}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 875}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 652}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 459}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 494}], "start": **********.246611, "duration": 0.11684, "duration_str": "117ms", "memory": 0, "memory_str": null, "filename": "UniqueManyToMany.php:55", "source": {"index": 16, "namespace": null, "name": "app/Rules/UniqueManyToMany.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Rules\\UniqueManyToMany.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FRules%2FUniqueManyToMany.php&line=55", "ajax": false, "filename": "UniqueManyToMany.php", "line": "55"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 4.132, "width_percent": 1.389}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 230}, {"index": 10, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.478158, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "Organization.php:230", "source": {"index": 9, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 230}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=230", "ajax": false, "filename": "Organization.php", "line": "230"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 5.521, "width_percent": 0}, {"sql": "insert into `organizations` (`shop_id`, `name`, `region`, `units`, `currency`, `updated_at`, `created_at`) values (null, 'Tanzayb', null, null, null, '2025-07-04 09:19:51', '2025-07-04 09:19:51')", "type": "query", "params": [], "bindings": [null, "<PERSON><PERSON><PERSON><PERSON>", null, null, null, "2025-07-04 09:19:51", "2025-07-04 09:19:51"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.479238, "duration": 0.*****************, "duration_str": "128ms", "memory": 0, "memory_str": null, "filename": "Organization.php:245", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=245", "ajax": false, "filename": "Organization.php", "line": "245"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 5.521, "width_percent": 1.527}, {"sql": "insert into `channels` (`organization_id`, `name`, `type`, `updated_at`, `created_at`) values (1, 'Tanzayb Store', 'shopify', '2025-07-04 09:19:51', '2025-07-04 09:19:51')", "type": "query", "params": [], "bindings": [1, "Tanzayb Store", "shopify", "2025-07-04 09:19:51", "2025-07-04 09:19:51"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 328}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 23, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.615225, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "Organization.php:328", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 328}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=328", "ajax": false, "filename": "Organization.php", "line": "328"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 7.049, "width_percent": 0.01}, {"sql": "select count(*) as aggregate from `locations` where `organization_id` = 1 and `organization_id` = 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Location/Location.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Location\\Location.php", "line": 36}, {"index": 24, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 334}, {"index": 25, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 32, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 33, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}], "start": **********.790844, "duration": 0.19541999999999998, "duration_str": "195ms", "memory": 0, "memory_str": null, "filename": "Location.php:36", "source": {"index": 16, "namespace": null, "name": "app/Models/Location/Location.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Location\\Location.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FLocation%2FLocation.php&line=36", "ajax": false, "filename": "Location.php", "line": "36"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 7.058, "width_percent": 2.324}, {"sql": "insert into `locations` (`organization_id`, `name`, `default_location`, `updated_at`, `created_at`) values (1, 'Tanzayb Store Warehouse', 1, '2025-07-04 09:19:51', '2025-07-04 09:19:51')", "type": "query", "params": [], "bindings": [1, "Tanzayb Store Warehouse", 1, "2025-07-04 09:19:51", "2025-07-04 09:19:51"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 334}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 23, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.989698, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "Organization.php:334", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 334}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=334", "ajax": false, "filename": "Organization.php", "line": "334"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 9.382, "width_percent": 0.009}, {"sql": "insert into `channel_location` (`channel_id`, `location_id`, `updated_at`, `created_at`) values (1, 1, '2025-07-04 09:19:52', '2025-07-04 09:19:52')", "type": "query", "params": [], "bindings": [1, 1, "2025-07-04 09:19:52", "2025-07-04 09:19:52"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 339}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 23, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.0254412, "duration": 0.09983, "duration_str": "99.83ms", "memory": 0, "memory_str": null, "filename": "Organization.php:339", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 339}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=339", "ajax": false, "filename": "Organization.php", "line": "339"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 9.391, "width_percent": 1.187}, {"sql": "insert into `versions` (`name`, `organization_id`, `is_default`, `currency`, `separator`, `updated_at`, `created_at`) values ('EN-US', 1, 1, 'USD', '.', '2025-07-04 09:19:52', '2025-07-04 09:19:52')", "type": "query", "params": [], "bindings": ["EN-US", 1, 1, "USD", ".", "2025-07-04 09:19:52", "2025-07-04 09:19:52"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 349}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 23, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.132816, "duration": 0.10114, "duration_str": "101ms", "memory": 0, "memory_str": null, "filename": "Organization.php:349", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 349}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=349", "ajax": false, "filename": "Organization.php", "line": "349"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 10.578, "width_percent": 1.203}, {"sql": "insert into `channel_version` (`channel_id`, `version_id`, `updated_at`, `created_at`) values (1, 1, '2025-07-04 09:19:52', '2025-07-04 09:19:52')", "type": "query", "params": [], "bindings": [1, 1, "2025-07-04 09:19:52", "2025-07-04 09:19:52"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 355}, {"index": 14, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 21, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.271728, "duration": 0.1656, "duration_str": "166ms", "memory": 0, "memory_str": null, "filename": "Organization.php:355", "source": {"index": 13, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 355}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=355", "ajax": false, "filename": "Organization.php", "line": "355"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 11.781, "width_percent": 1.969}, {"sql": "insert into `folders` (`name`, `organization_id`, `is_default`, `updated_at`, `created_at`) values ('Tanzayb', 1, 1, '2025-07-04 09:19:52', '2025-07-04 09:19:52')", "type": "query", "params": [], "bindings": ["<PERSON><PERSON><PERSON><PERSON>", 1, 1, "2025-07-04 09:19:52", "2025-07-04 09:19:52"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "packages/apimio/gallery/src/Models/Folder.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\gallery\\src\\Models\\Folder.php", "line": 228}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 359}, {"index": 17, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 24, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}], "start": **********.4876258, "duration": 0.13857, "duration_str": "139ms", "memory": 0, "memory_str": null, "filename": "Folder.php:228", "source": {"index": 15, "namespace": null, "name": "packages/apimio/gallery/src/Models/Folder.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\gallery\\src\\Models\\Folder.php", "line": 228}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fpackages%2Fapimio%2Fgallery%2Fsrc%2FModels%2FFolder.php&line=228", "ajax": false, "filename": "Folder.php", "line": "228"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 13.75, "width_percent": 1.648}, {"sql": "update `folders` set `link` = 'guests/eyJpdiI6InFJZmpIS1BZMVRPajQ0SWhuU3pTU3c9PSIsInZhbHVlIjoiOE5BckJ5K2VWUy9Xdll6TlZLSit4QT09IiwibWFjIjoiMTViMDU1OWI4ZmJkN2Q3MzBlMGQwNjVhZjRjYjgyOGI1NzlhMzJlMDQ0OTBiYmNiZDE2MzExOWU3MzZiNjFkMCIsInRhZyI6IiJ9', `folders`.`updated_at` = '2025-07-04 09:19:52' where `id` = 1 and `organization_id` = 1", "type": "query", "params": [], "bindings": ["guests/eyJpdiI6InFJZmpIS1BZMVRPajQ0SWhuU3pTU3c9PSIsInZhbHVlIjoiOE5BckJ5K2VWUy9Xdll6TlZLSit4QT09IiwibWFjIjoiMTViMDU1OWI4ZmJkN2Q3MzBlMGQwNjVhZjRjYjgyOGI1NzlhMzJlMDQ0OTBiYmNiZDE2MzExOWU3MzZiNjFkMCIsInRhZyI6IiJ9", "2025-07-04 09:19:52", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "packages/apimio/gallery/src/Models/Folder.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\gallery\\src\\Models\\Folder.php", "line": 139}, {"index": 13, "namespace": null, "name": "packages/apimio/gallery/src/Models/Folder.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\gallery\\src\\Models\\Folder.php", "line": 230}, {"index": 14, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 359}, {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 22, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}], "start": **********.632998, "duration": 0.04346, "duration_str": "43.46ms", "memory": 0, "memory_str": null, "filename": "Folder.php:139", "source": {"index": 12, "namespace": null, "name": "packages/apimio/gallery/src/Models/Folder.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\gallery\\src\\Models\\Folder.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fpackages%2Fapimio%2Fgallery%2Fsrc%2FModels%2FFolder.php&line=139", "ajax": false, "filename": "Folder.php", "line": "139"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 15.397, "width_percent": 0.517}, {"sql": "insert into `folders` (`name`, `organization_id`, `is_default`, `type`, `updated_at`, `created_at`) values ('Instagram', 1, 1, 'social_media', '2025-07-04 09:19:52', '2025-07-04 09:19:52')", "type": "query", "params": [], "bindings": ["Instagram", 1, 1, "social_media", "2025-07-04 09:19:52", "2025-07-04 09:19:52"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "packages/apimio/gallery/src/Models/Folder.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\gallery\\src\\Models\\Folder.php", "line": 242}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 364}, {"index": 17, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 24, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}], "start": **********.6820228, "duration": 0.22041, "duration_str": "220ms", "memory": 0, "memory_str": null, "filename": "Folder.php:242", "source": {"index": 15, "namespace": null, "name": "packages/apimio/gallery/src/Models/Folder.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\gallery\\src\\Models\\Folder.php", "line": 242}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fpackages%2Fapimio%2Fgallery%2Fsrc%2FModels%2FFolder.php&line=242", "ajax": false, "filename": "Folder.php", "line": "242"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 15.914, "width_percent": 2.621}, {"sql": "update `folders` set `link` = 'guests/eyJpdiI6InNVdnl4eUtIRWh5WVNzWHR0NkNqL3c9PSIsInZhbHVlIjoiUlZqNFBneFNZdlhsdmFMMGszcXA4UT09IiwibWFjIjoiMjIyYmRmY2Q0OWE2Yjk4YzZmNjk1YWU3MGUwNWJjNThlYjI0MzljMmUxNjViY2ZkYjQ4YjViMDY0OTdmOTNiMiIsInRhZyI6IiJ9', `folders`.`updated_at` = '2025-07-04 09:19:52' where `id` = 2 and `organization_id` = 1", "type": "query", "params": [], "bindings": ["guests/eyJpdiI6InNVdnl4eUtIRWh5WVNzWHR0NkNqL3c9PSIsInZhbHVlIjoiUlZqNFBneFNZdlhsdmFMMGszcXA4UT09IiwibWFjIjoiMjIyYmRmY2Q0OWE2Yjk4YzZmNjk1YWU3MGUwNWJjNThlYjI0MzljMmUxNjViY2ZkYjQ4YjViMDY0OTdmOTNiMiIsInRhZyI6IiJ9", "2025-07-04 09:19:52", 2, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "packages/apimio/gallery/src/Models/Folder.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\gallery\\src\\Models\\Folder.php", "line": 139}, {"index": 13, "namespace": null, "name": "packages/apimio/gallery/src/Models/Folder.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\gallery\\src\\Models\\Folder.php", "line": 244}, {"index": 14, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 364}, {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 22, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}], "start": **********.906814, "duration": 0.09368000000000001, "duration_str": "93.68ms", "memory": 0, "memory_str": null, "filename": "Folder.php:139", "source": {"index": 12, "namespace": null, "name": "packages/apimio/gallery/src/Models/Folder.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\gallery\\src\\Models\\Folder.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fpackages%2Fapimio%2Fgallery%2Fsrc%2FModels%2FFolder.php&line=139", "ajax": false, "filename": "Folder.php", "line": "139"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 18.535, "width_percent": 1.114}, {"sql": "insert into `folders` (`name`, `organization_id`, `is_default`, `type`, `updated_at`, `created_at`) values ('Youtube', 1, 1, 'social_media', '2025-07-04 09:19:53', '2025-07-04 09:19:53')", "type": "query", "params": [], "bindings": ["Youtube", 1, 1, "social_media", "2025-07-04 09:19:53", "2025-07-04 09:19:53"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "packages/apimio/gallery/src/Models/Folder.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\gallery\\src\\Models\\Folder.php", "line": 242}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 364}, {"index": 17, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 24, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}], "start": **********.005098, "duration": 0.10439, "duration_str": "104ms", "memory": 0, "memory_str": null, "filename": "Folder.php:242", "source": {"index": 15, "namespace": null, "name": "packages/apimio/gallery/src/Models/Folder.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\gallery\\src\\Models\\Folder.php", "line": 242}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fpackages%2Fapimio%2Fgallery%2Fsrc%2FModels%2FFolder.php&line=242", "ajax": false, "filename": "Folder.php", "line": "242"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 19.649, "width_percent": 1.241}, {"sql": "update `folders` set `link` = 'guests/eyJpdiI6IkpVMU5ZQUlNcXd1YU1takRMWnNwMVE9PSIsInZhbHVlIjoiSmZHSnV1YVcwdGUyVEVjUE1EcUJ0UT09IiwibWFjIjoiMmVkMTFjYzI1YzU3ZTRmZTQyNjc0NDk3YWNjYjAzNmY4YTkxYmFjMmI3OGQzZTI5OGYwYmM1YjBhODQ3OTc2MiIsInRhZyI6IiJ9', `folders`.`updated_at` = '2025-07-04 09:19:53' where `id` = 3 and `organization_id` = 1", "type": "query", "params": [], "bindings": ["guests/eyJpdiI6IkpVMU5ZQUlNcXd1YU1takRMWnNwMVE9PSIsInZhbHVlIjoiSmZHSnV1YVcwdGUyVEVjUE1EcUJ0UT09IiwibWFjIjoiMmVkMTFjYzI1YzU3ZTRmZTQyNjc0NDk3YWNjYjAzNmY4YTkxYmFjMmI3OGQzZTI5OGYwYmM1YjBhODQ3OTc2MiIsInRhZyI6IiJ9", "2025-07-04 09:19:53", 3, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "packages/apimio/gallery/src/Models/Folder.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\gallery\\src\\Models\\Folder.php", "line": 139}, {"index": 13, "namespace": null, "name": "packages/apimio/gallery/src/Models/Folder.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\gallery\\src\\Models\\Folder.php", "line": 244}, {"index": 14, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 364}, {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 22, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}], "start": **********.115455, "duration": 0.1056, "duration_str": "106ms", "memory": 0, "memory_str": null, "filename": "Folder.php:139", "source": {"index": 12, "namespace": null, "name": "packages/apimio/gallery/src/Models/Folder.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\gallery\\src\\Models\\Folder.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fpackages%2Fapimio%2Fgallery%2Fsrc%2FModels%2FFolder.php&line=139", "ajax": false, "filename": "Folder.php", "line": "139"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 20.89, "width_percent": 1.256}, {"sql": "insert into `folders` (`name`, `organization_id`, `is_default`, `type`, `updated_at`, `created_at`) values ('Facebook', 1, 1, 'social_media', '2025-07-04 09:19:53', '2025-07-04 09:19:53')", "type": "query", "params": [], "bindings": ["Facebook", 1, 1, "social_media", "2025-07-04 09:19:53", "2025-07-04 09:19:53"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "packages/apimio/gallery/src/Models/Folder.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\gallery\\src\\Models\\Folder.php", "line": 242}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 364}, {"index": 17, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 24, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}], "start": **********.226517, "duration": 0.20115, "duration_str": "201ms", "memory": 0, "memory_str": null, "filename": "Folder.php:242", "source": {"index": 15, "namespace": null, "name": "packages/apimio/gallery/src/Models/Folder.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\gallery\\src\\Models\\Folder.php", "line": 242}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fpackages%2Fapimio%2Fgallery%2Fsrc%2FModels%2FFolder.php&line=242", "ajax": false, "filename": "Folder.php", "line": "242"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 22.146, "width_percent": 2.392}, {"sql": "update `folders` set `link` = 'guests/eyJpdiI6IkFxd2hBdExMTEVGWkpBbTNHRG04Tmc9PSIsInZhbHVlIjoiZnpnQ0VEUGxHMjFZd3hkUmVteTJIdz09IiwibWFjIjoiOGIyNmY1NmVlMDJjNDc1ZWRiMWE3MTk5ZDY1YzEwNzFmNTE4YmM2ZThhZGExMzZiMDQyYzM4YmFjZjA0NjIxOCIsInRhZyI6IiJ9', `folders`.`updated_at` = '2025-07-04 09:19:53' where `id` = 4 and `organization_id` = 1", "type": "query", "params": [], "bindings": ["guests/eyJpdiI6IkFxd2hBdExMTEVGWkpBbTNHRG04Tmc9PSIsInZhbHVlIjoiZnpnQ0VEUGxHMjFZd3hkUmVteTJIdz09IiwibWFjIjoiOGIyNmY1NmVlMDJjNDc1ZWRiMWE3MTk5ZDY1YzEwNzFmNTE4YmM2ZThhZGExMzZiMDQyYzM4YmFjZjA0NjIxOCIsInRhZyI6IiJ9", "2025-07-04 09:19:53", 4, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "packages/apimio/gallery/src/Models/Folder.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\gallery\\src\\Models\\Folder.php", "line": 139}, {"index": 13, "namespace": null, "name": "packages/apimio/gallery/src/Models/Folder.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\gallery\\src\\Models\\Folder.php", "line": 244}, {"index": 14, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 364}, {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 22, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}], "start": **********.433082, "duration": 0.22294999999999998, "duration_str": "223ms", "memory": 0, "memory_str": null, "filename": "Folder.php:139", "source": {"index": 12, "namespace": null, "name": "packages/apimio/gallery/src/Models/Folder.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\gallery\\src\\Models\\Folder.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fpackages%2Fapimio%2Fgallery%2Fsrc%2FModels%2FFolder.php&line=139", "ajax": false, "filename": "Folder.php", "line": "139"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 24.537, "width_percent": 2.651}, {"sql": "insert into `families` (`name`, `is_default`, `organization_id`, `updated_at`, `created_at`) values ('General', 1, 1, '2025-07-04 09:19:53', '2025-07-04 09:19:53')", "type": "query", "params": [], "bindings": ["General", 1, 1, "2025-07-04 09:19:53", "2025-07-04 09:19:53"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 371}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 23, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.7089221, "duration": 0.*****************, "duration_str": "195ms", "memory": 0, "memory_str": null, "filename": "Organization.php:371", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 371}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=371", "ajax": false, "filename": "Organization.php", "line": "371"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 27.188, "width_percent": 2.314}, {"sql": "insert into `settings` (`key`, `organization_id`, `value`) values ('sku', 1, '1'), ('name', 1, '1'), ('barcode', 1, '0'), ('weight', 1, '1'), ('price', 1, '1'), ('compare_at_price', 1, '0'), ('cost_price', 1, '0'), ('brand', 1, '1'), ('vendor', 1, '1'), ('category', 1, '1')", "type": "query", "params": [], "bindings": ["sku", 1, "1", "name", 1, "1", "barcode", 1, "0", "weight", 1, "1", "price", 1, "1", "compare_at_price", 1, "0", "cost_price", 1, "0", "brand", 1, "1", "vendor", 1, "1", "category", 1, "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 373}, {"index": 11, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 18, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.908268, "duration": 0.056229999999999995, "duration_str": "56.23ms", "memory": 0, "memory_str": null, "filename": "Organization.php:373", "source": {"index": 10, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 373}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=373", "ajax": false, "filename": "Organization.php", "line": "373"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 29.503, "width_percent": 0.669}, {"sql": "insert into `attributes` (`name`, `handle`, `attribute_type_id`, `organization_id`, `rules`, `is_default`, `updated_at`, `created_at`) values ('Product Name', 'product_name', 1, 1, '{\\\"required\\\":1,\\\"max\\\":255}', 1, '2025-07-04 09:19:54', '2025-07-04 09:19:54')", "type": "query", "params": [], "bindings": ["Product Name", "product_name", 1, 1, "{\"required\":1,\"max\":255}", 1, "2025-07-04 09:19:54", "2025-07-04 09:19:54"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 434}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 23, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.017693, "duration": 0.12755, "duration_str": "128ms", "memory": 0, "memory_str": null, "filename": "Organization.php:434", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 434}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=434", "ajax": false, "filename": "Organization.php", "line": "434"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 30.171, "width_percent": 1.517}, {"sql": "insert into `attribute_family` (`family_id`, `attribute_id`, `updated_at`, `created_at`) values (1, 1, '2025-07-04 09:19:54', '2025-07-04 09:19:54')", "type": "query", "params": [], "bindings": [1, 1, "2025-07-04 09:19:54", "2025-07-04 09:19:54"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 439}, {"index": 14, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 21, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.1926072, "duration": 0.09156, "duration_str": "91.56ms", "memory": 0, "memory_str": null, "filename": "Organization.php:439", "source": {"index": 13, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 439}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=439", "ajax": false, "filename": "Organization.php", "line": "439"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 31.688, "width_percent": 1.089}, {"sql": "insert into `attributes` (`name`, `handle`, `attribute_type_id`, `organization_id`, `rules`, `is_default`, `updated_at`, `created_at`) values ('Title', 'title', 13, 1, null, 1, '2025-07-04 09:19:54', '2025-07-04 09:19:54')", "type": "query", "params": [], "bindings": ["Title", "title", 13, 1, null, 1, "2025-07-04 09:19:54", "2025-07-04 09:19:54"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 449}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 23, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.289135, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Organization.php:449", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 449}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=449", "ajax": false, "filename": "Organization.php", "line": "449"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 32.777, "width_percent": 0.006}, {"sql": "insert into `attribute_options` (`name`, `attribute_id`, `updated_at`, `created_at`) values ('Default Title', 2, '2025-07-04 09:19:54', '2025-07-04 09:19:54')", "type": "query", "params": [], "bindings": ["Default Title", 2, "2025-07-04 09:19:54", "2025-07-04 09:19:54"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 454}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 23, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.3323119, "duration": 0.09997, "duration_str": "99.97ms", "memory": 0, "memory_str": null, "filename": "Organization.php:454", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 454}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=454", "ajax": false, "filename": "Organization.php", "line": "454"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 32.783, "width_percent": 1.189}, {"sql": "insert into `attributes` (`name`, `handle`, `attribute_type_id`, `organization_id`, `rules`, `is_default`, `updated_at`, `created_at`) values ('Description', 'description', 3, 1, '{\\\"required\\\":1,\\\"max\\\":63000}', 1, '2025-07-04 09:19:54', '2025-07-04 09:19:54')", "type": "query", "params": [], "bindings": ["Description", "description", 3, 1, "{\"required\":1,\"max\":63000}", 1, "2025-07-04 09:19:54", "2025-07-04 09:19:54"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 463}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 23, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.436459, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Organization.php:463", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 463}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=463", "ajax": false, "filename": "Organization.php", "line": "463"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 33.971, "width_percent": 0.006}, {"sql": "insert into `attribute_family` (`family_id`, `attribute_id`, `updated_at`, `created_at`) values (1, 3, '2025-07-04 09:19:54', '2025-07-04 09:19:54')", "type": "query", "params": [], "bindings": [1, 3, "2025-07-04 09:19:54", "2025-07-04 09:19:54"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 468}, {"index": 14, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 21, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.441879, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Organization.php:468", "source": {"index": 13, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 468}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=468", "ajax": false, "filename": "Organization.php", "line": "468"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 33.977, "width_percent": 0.006}, {"sql": "insert into `families` (`name`, `is_default`, `organization_id`, `updated_at`, `created_at`) values ('SEO', 1, 1, '2025-07-04 09:19:54', '2025-07-04 09:19:54')", "type": "query", "params": [], "bindings": ["SEO", 1, 1, "2025-07-04 09:19:54", "2025-07-04 09:19:54"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 476}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 23, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.447648, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Organization.php:476", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 476}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=476", "ajax": false, "filename": "Organization.php", "line": "476"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 33.983, "width_percent": 0.006}, {"sql": "insert into `attributes` (`name`, `handle`, `attribute_type_id`, `organization_id`, `rules`, `is_default`, `updated_at`, `created_at`) values ('URL Slug', 'seo_url', 11, 1, '{\\\"required\\\":1,\\\"0\\\":\\\"slug\\\",\\\"1\\\":\\\"url\\\",\\\"max\\\":255}', 1, '2025-07-04 09:19:54', '2025-07-04 09:19:54')", "type": "query", "params": [], "bindings": ["URL Slug", "seo_url", 11, 1, "{\"required\":1,\"0\":\"slug\",\"1\":\"url\",\"max\":255}", 1, "2025-07-04 09:19:54", "2025-07-04 09:19:54"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 486}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 23, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.452943, "duration": 0.07682, "duration_str": "76.82ms", "memory": 0, "memory_str": null, "filename": "Organization.php:486", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 486}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=486", "ajax": false, "filename": "Organization.php", "line": "486"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 33.989, "width_percent": 0.913}, {"sql": "insert into `attribute_family` (`family_id`, `attribute_id`, `updated_at`, `created_at`) values (2, 4, '2025-07-04 09:19:54', '2025-07-04 09:19:54')", "type": "query", "params": [], "bindings": [2, 4, "2025-07-04 09:19:54", "2025-07-04 09:19:54"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 491}, {"index": 14, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 21, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.5347269, "duration": 0.20298, "duration_str": "203ms", "memory": 0, "memory_str": null, "filename": "Organization.php:491", "source": {"index": 13, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 491}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=491", "ajax": false, "filename": "Organization.php", "line": "491"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 34.903, "width_percent": 2.414}, {"sql": "insert into `attributes` (`name`, `handle`, `attribute_type_id`, `organization_id`, `rules`, `is_default`, `updated_at`, `created_at`) values ('SEO Title', 'seo_title', 1, 1, '{\\\"required\\\":1,\\\"max\\\":255}', 1, '2025-07-04 09:19:54', '2025-07-04 09:19:54')", "type": "query", "params": [], "bindings": ["SEO Title", "seo_title", 1, 1, "{\"required\":1,\"max\":255}", 1, "2025-07-04 09:19:54", "2025-07-04 09:19:54"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 501}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 23, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.742407, "duration": 0.31439, "duration_str": "314ms", "memory": 0, "memory_str": null, "filename": "Organization.php:501", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 501}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=501", "ajax": false, "filename": "Organization.php", "line": "501"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 37.316, "width_percent": 3.738}, {"sql": "insert into `attribute_family` (`family_id`, `attribute_id`, `updated_at`, `created_at`) values (2, 5, '2025-07-04 09:19:55', '2025-07-04 09:19:55')", "type": "query", "params": [], "bindings": [2, 5, "2025-07-04 09:19:55", "2025-07-04 09:19:55"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 506}, {"index": 14, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 21, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.061025, "duration": 0.39716, "duration_str": "397ms", "memory": 0, "memory_str": null, "filename": "Organization.php:506", "source": {"index": 13, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 506}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=506", "ajax": false, "filename": "Organization.php", "line": "506"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 41.055, "width_percent": 4.722}, {"sql": "insert into `attributes` (`name`, `handle`, `attribute_type_id`, `organization_id`, `rules`, `is_default`, `updated_at`, `created_at`) values ('SEO Description', 'seo_description', 3, 1, '{\\\"required\\\":1,\\\"max\\\":160}', 1, '2025-07-04 09:19:55', '2025-07-04 09:19:55')", "type": "query", "params": [], "bindings": ["SEO Description", "seo_description", 3, 1, "{\"required\":1,\"max\":160}", 1, "2025-07-04 09:19:55", "2025-07-04 09:19:55"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 516}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 23, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.465019, "duration": 0.32992, "duration_str": "330ms", "memory": 0, "memory_str": null, "filename": "Organization.php:516", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 516}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=516", "ajax": false, "filename": "Organization.php", "line": "516"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 45.777, "width_percent": 3.923}, {"sql": "insert into `attribute_family` (`family_id`, `attribute_id`, `updated_at`, `created_at`) values (2, 6, '2025-07-04 09:19:55', '2025-07-04 09:19:55')", "type": "query", "params": [], "bindings": [2, 6, "2025-07-04 09:19:55", "2025-07-04 09:19:55"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 521}, {"index": 14, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 21, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.8002279, "duration": 0.44827, "duration_str": "448ms", "memory": 0, "memory_str": null, "filename": "Organization.php:521", "source": {"index": 13, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 521}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=521", "ajax": false, "filename": "Organization.php", "line": "521"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 49.7, "width_percent": 5.33}, {"sql": "insert into `attributes` (`name`, `handle`, `attribute_type_id`, `organization_id`, `rules`, `is_default`, `updated_at`, `created_at`) values ('Tags', 'seo_keyword', 1, 1, '{\\\"required\\\":1,\\\"max\\\":255}', 1, '2025-07-04 09:19:56', '2025-07-04 09:19:56')", "type": "query", "params": [], "bindings": ["Tags", "seo_keyword", 1, 1, "{\"required\":1,\"max\":255}", 1, "2025-07-04 09:19:56", "2025-07-04 09:19:56"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 531}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 23, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.253627, "duration": 0.*****************, "duration_str": "499ms", "memory": 0, "memory_str": null, "filename": "Organization.php:531", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 531}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=531", "ajax": false, "filename": "Organization.php", "line": "531"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 55.03, "width_percent": 5.933}, {"sql": "insert into `attribute_family` (`family_id`, `attribute_id`, `updated_at`, `created_at`) values (2, 7, '2025-07-04 09:19:56', '2025-07-04 09:19:56')", "type": "query", "params": [], "bindings": [2, 7, "2025-07-04 09:19:56", "2025-07-04 09:19:56"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 536}, {"index": 14, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 21, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.755944, "duration": 0.41054, "duration_str": "411ms", "memory": 0, "memory_str": null, "filename": "Organization.php:536", "source": {"index": 13, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 536}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=536", "ajax": false, "filename": "Organization.php", "line": "536"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 60.963, "width_percent": 4.882}, {"sql": "select `id` from `families` where `is_default` = 1 and `organization_id` = 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 554}, {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 537}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 23, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}], "start": 1751620797.1714022, "duration": 0.54926, "duration_str": "549ms", "memory": 0, "memory_str": null, "filename": "Organization.php:554", "source": {"index": 14, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 554}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=554", "ajax": false, "filename": "Organization.php", "line": "554"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 65.845, "width_percent": 6.531}, {"sql": "select * from `attribute_family` where `family_id` in (1, 2)", "type": "query", "params": [], "bindings": [1, 2], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 555}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 537}, {"index": 17, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 24, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}], "start": 1751620797.725071, "duration": 0.72577, "duration_str": "726ms", "memory": 0, "memory_str": null, "filename": "Organization.php:555", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 555}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=555", "ajax": false, "filename": "Organization.php", "line": "555"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 72.376, "width_percent": 8.63}, {"sql": "select * from `attributes` where `attributes`.`id` in (1, 3, 4, 5, 6, 7) and `organization_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 555}, {"index": 21, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 537}, {"index": 22, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 29, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 30, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}], "start": 1751620799.126471, "duration": 0.32566, "duration_str": "326ms", "memory": 0, "memory_str": null, "filename": "Organization.php:555", "source": {"index": 20, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 555}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=555", "ajax": false, "filename": "Organization.php", "line": "555"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 81.005, "width_percent": 3.872}, {"sql": "insert into `product_shopify_mappings` (`attribute_family_id`, `organization_id`, `shopify_fields`, `updated_at`, `created_at`) values (1, 1, 'title', '2025-07-04 09:19:59', '2025-07-04 09:19:59')", "type": "query", "params": [], "bindings": [1, 1, "title", "2025-07-04 09:19:59", "2025-07-04 09:19:59"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 563}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 537}, {"index": 17, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 24, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}], "start": 1751620799.489966, "duration": 0.28607, "duration_str": "286ms", "memory": 0, "memory_str": null, "filename": "Organization.php:563", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 563}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=563", "ajax": false, "filename": "Organization.php", "line": "563"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 84.878, "width_percent": 3.402}, {"sql": "insert into `product_shopify_mappings` (`attribute_family_id`, `organization_id`, `shopify_fields`, `updated_at`, `created_at`) values (2, 1, 'body_html', '2025-07-04 09:19:59', '2025-07-04 09:19:59')", "type": "query", "params": [], "bindings": [2, 1, "body_html", "2025-07-04 09:19:59", "2025-07-04 09:19:59"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 566}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 537}, {"index": 17, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 24, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}], "start": 1751620799.781243, "duration": 0.30595, "duration_str": "306ms", "memory": 0, "memory_str": null, "filename": "Organization.php:566", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 566}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=566", "ajax": false, "filename": "Organization.php", "line": "566"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 88.279, "width_percent": 3.638}, {"sql": "insert into `product_shopify_mappings` (`attribute_family_id`, `organization_id`, `shopify_fields`, `updated_at`, `created_at`) values (4, 1, 'metafields_global_title_tag', '2025-07-04 09:20:00', '2025-07-04 09:20:00')", "type": "query", "params": [], "bindings": [4, 1, "metafields_global_title_tag", "2025-07-04 09:20:00", "2025-07-04 09:20:00"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 581}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 537}, {"index": 17, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 24, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}], "start": **********.091994, "duration": 0.26080000000000003, "duration_str": "261ms", "memory": 0, "memory_str": null, "filename": "Organization.php:581", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 581}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=581", "ajax": false, "filename": "Organization.php", "line": "581"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 91.917, "width_percent": 3.101}, {"sql": "insert into `product_shopify_mappings` (`attribute_family_id`, `organization_id`, `shopify_fields`, `updated_at`, `created_at`) values (5, 1, 'metafields_global_description_tag', '2025-07-04 09:20:00', '2025-07-04 09:20:00')", "type": "query", "params": [], "bindings": [5, 1, "metafields_global_description_tag", "2025-07-04 09:20:00", "2025-07-04 09:20:00"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 584}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 537}, {"index": 17, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 24, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}], "start": **********.357443, "duration": 0.22383, "duration_str": "224ms", "memory": 0, "memory_str": null, "filename": "Organization.php:584", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 584}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=584", "ajax": false, "filename": "Organization.php", "line": "584"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 95.018, "width_percent": 2.661}, {"sql": "select count(*) as aggregate from `invites` where `email` = '<EMAIL>' and `is_accepted` = '0' and `is_declined` = '0'", "type": "query", "params": [], "bindings": ["<EMAIL>", "0", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Invite/Invite.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Invite\\Invite.php", "line": 124}, {"index": 17, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 544}, {"index": 18, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 25, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}], "start": **********.655294, "duration": 0.17466, "duration_str": "175ms", "memory": 0, "memory_str": null, "filename": "Invite.php:124", "source": {"index": 16, "namespace": null, "name": "app/Models/Invite/Invite.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Invite\\Invite.php", "line": 124}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FInvite%2FInvite.php&line=124", "ajax": false, "filename": "Invite.php", "line": "124"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 97.679, "width_percent": 2.077}, {"sql": "select * from `invites` where `email` = '<EMAIL>'", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Invite/Invite.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Invite\\Invite.php", "line": 486}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 250}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.835023, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Invite.php:486", "source": {"index": 15, "namespace": null, "name": "app/Models/Invite/Invite.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Invite\\Invite.php", "line": 486}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FInvite%2FInvite.php&line=486", "ajax": false, "filename": "Invite.php", "line": "486"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 99.756, "width_percent": 0.006}, {"sql": "update `organizations` set `stripe_id` = 'cus_ScKRwY9l19vf71', `organizations`.`updated_at` = '2025-07-04 09:20:16' where `id` = 1", "type": "query", "params": [], "bindings": ["cus_ScKRwY9l19vf71", "2025-07-04 09:20:16", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/laravel/cashier/src/Concerns/ManagesCustomer.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\cashier\\src\\Concerns\\ManagesCustomer.php", "line": 97}, {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 252}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.204479, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "ManagesCustomer.php:97", "source": {"index": 14, "namespace": null, "name": "vendor/laravel/cashier/src/Concerns/ManagesCustomer.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\cashier\\src\\Concerns\\ManagesCustomer.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fvendor%2Flaravel%2Fcashier%2Fsrc%2FConcerns%2FManagesCustomer.php&line=97", "ajax": false, "filename": "ManagesCustomer.php", "line": "97"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 99.763, "width_percent": 0.009}, {"sql": "update `organizations` set `trial_ends_at` = '2025-07-18 09:20:16', `organizations`.`updated_at` = '2025-07-04 09:20:16' where `id` = 1", "type": "query", "params": [], "bindings": ["2025-07-18 09:20:16", "2025-07-04 09:20:16", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 254}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.211123, "duration": 0.0057, "duration_str": "5.7ms", "memory": 0, "memory_str": null, "filename": "Organization.php:254", "source": {"index": 14, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 254}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=254", "ajax": false, "filename": "Organization.php", "line": "254"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 99.772, "width_percent": 0.068}, {"sql": "select * from `users` where `users`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 263}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.220335, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "Organization.php:263", "source": {"index": 21, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 263}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=263", "ajax": false, "filename": "Organization.php", "line": "263"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 99.839, "width_percent": 0.007}, {"sql": "select * from `organization_user` where `organization_user`.`organization_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 277}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.226254, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "Organization.php:277", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 277}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=277", "ajax": false, "filename": "Organization.php", "line": "277"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 99.846, "width_percent": 0.014}, {"sql": "insert into `organization_user` (`organization_id`, `user_id`) values (1, 1)", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 277}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.230385, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "Organization.php:277", "source": {"index": 13, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 277}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=277", "ajax": false, "filename": "Organization.php", "line": "277"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 99.86, "width_percent": 0.012}, {"sql": "select `id` from `organization_user` where `user_id` = 1 and `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 280}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.2358549, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Organization.php:280", "source": {"index": 17, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 280}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=280", "ajax": false, "filename": "Organization.php", "line": "280"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 99.872, "width_percent": 0.007}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 283}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.240177, "duration": 0.00175, "duration_str": "1.75ms", "memory": 0, "memory_str": null, "filename": "Organization.php:283", "source": {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 283}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=283", "ajax": false, "filename": "Organization.php", "line": "283"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 99.879, "width_percent": 0.021}, {"sql": "insert into `organization_user_permissions` (`organization_user_id`, `permission_id`, `updated_at`, `created_at`) values (1, 1, '2025-07-04 09:20:16', '2025-07-04 09:20:16')", "type": "query", "params": [], "bindings": [1, 1, "2025-07-04 09:20:16", "2025-07-04 09:20:16"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 290}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.273096, "duration": 0.00296, "duration_str": "2.96ms", "memory": 0, "memory_str": null, "filename": "Organization.php:290", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 290}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=290", "ajax": false, "filename": "Organization.php", "line": "290"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 99.9, "width_percent": 0.035}, {"sql": "insert into `organization_user_permissions` (`organization_user_id`, `permission_id`, `updated_at`, `created_at`) values (1, 2, '2025-07-04 09:20:16', '2025-07-04 09:20:16')", "type": "query", "params": [], "bindings": [1, 2, "2025-07-04 09:20:16", "2025-07-04 09:20:16"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 290}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.2803051, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Organization.php:290", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 290}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=290", "ajax": false, "filename": "Organization.php", "line": "290"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 99.935, "width_percent": 0.006}, {"sql": "insert into `organization_user_permissions` (`organization_user_id`, `permission_id`, `updated_at`, `created_at`) values (1, 3, '2025-07-04 09:20:16', '2025-07-04 09:20:16')", "type": "query", "params": [], "bindings": [1, 3, "2025-07-04 09:20:16", "2025-07-04 09:20:16"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 290}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.284433, "duration": 0.00411, "duration_str": "4.11ms", "memory": 0, "memory_str": null, "filename": "Organization.php:290", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 290}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=290", "ajax": false, "filename": "Organization.php", "line": "290"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 99.941, "width_percent": 0.049}, {"sql": "insert into `organization_user_permissions` (`organization_user_id`, `permission_id`, `updated_at`, `created_at`) values (1, 4, '2025-07-04 09:20:16', '2025-07-04 09:20:16')", "type": "query", "params": [], "bindings": [1, 4, "2025-07-04 09:20:16", "2025-07-04 09:20:16"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 290}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.294051, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Organization.php:290", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 290}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=290", "ajax": false, "filename": "Organization.php", "line": "290"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 99.99, "width_percent": 0.005}, {"sql": "insert into `organization_user_permissions` (`organization_user_id`, `permission_id`, `updated_at`, `created_at`) values (1, 5, '2025-07-04 09:20:16', '2025-07-04 09:20:16')", "type": "query", "params": [], "bindings": [1, 5, "2025-07-04 09:20:16", "2025-07-04 09:20:16"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 290}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.29841, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "Organization.php:290", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 290}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=290", "ajax": false, "filename": "Organization.php", "line": "290"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 99.995, "width_percent": 0.005}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 298}, {"index": 10, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.311042, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "Organization.php:298", "source": {"index": 9, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 298}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=298", "ajax": false, "filename": "Organization.php", "line": "298"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 100, "width_percent": 0}]}, "models": {"data": {"App\\Models\\Product\\AttributeFamily": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FAttributeFamily.php&line=1", "ajax": false, "filename": "AttributeFamily.php", "line": "?"}}, "App\\Models\\Product\\Attribute": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FAttribute.php&line=1", "ajax": false, "filename": "Attribute.php", "line": "?"}}, "App\\Models\\Organization\\Permission": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Product\\Family": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FFamily.php&line=1", "ajax": false, "filename": "Family.php", "line": "?"}}, "App\\Models\\Organization\\OrganizationUser": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganizationUser.php&line=1", "ajax": false, "filename": "OrganizationUser.php", "line": "?"}}}, "count": 22, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "ikPoMer4OtoDULeT9sBt2cHa7xhxWBWDS3oAnx7H", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/onboarding\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "password_hash_web": "null", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/organization/select\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]", "organization_id": "1"}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/api/2024-12/organization", "action_name": "organization.store", "controller_action": "App\\Http\\Controllers\\Api\\OrganizationController@store", "uri": "POST api/2024-12/organization", "controller": "App\\Http\\Controllers\\Api\\OrganizationController@store<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FOrganizationController.php&line=67\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers", "prefix": "api/2024-12", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FOrganizationController.php&line=67\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Api/OrganizationController.php:67-111</a>", "middleware": "api", "telescope": "<a href=\"http://localhost:8000/_debugbar/telescope/9f4f19a7-72ab-4231-8444-432fb6f8f063\" target=\"_blank\">View in Telescope</a>", "duration": "34.47s", "peak_memory": "40MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1196330927 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1196330927\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1332957046 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Tanzayb</span>\"\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"5 characters\">EN-US</span>\"\n  \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">USD</span>\"\n  \"<span class=sf-dump-key>separator</span>\" => \"<span class=sf-dump-str>.</span>\"\n  \"<span class=sf-dump-key>weightUnit</span>\" => \"<span class=sf-dump-str title=\"2 characters\">oz</span>\"\n  \"<span class=sf-dump-key>trial_ends_at</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-07-18</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1332957046\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">88</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">Bearer null******</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6InhsSFpKMHgwOTJLNnJyajJSbWdsNFE9PSIsInZhbHVlIjoic2VKRW5md3pxSEpaRlN1T2RKWUJLNWVGbDFZVWN2SmlQajRGVkROZ3ZzLzdiV0g4cWh4U0hYd3NUaGQ5aXFob2hKQlZzVGJsdkRESW1wSzd2WDRFVFRaNUpHdGdkSXFYN1c5OXV5S21veitBelBoM2FLTENDQXB1b2k0Q0djL08iLCJtYWMiOiJmOTQ0YTllNmU2M2E1ZTk0NDI2YjA4OGU4YTM3M2MwYzA3OTNlM2YyZTIxM2IzMjcxMTBkNjBiZDE1ZjAxYTdlIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">http://localhost:8000/onboarding</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1205 characters\">__stripe_mid=5fa877f3-5d7b-4255-b2c2-ced2f9ae1950465a52; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Ikh6c1lPTHd2QXUrT2F0OWs5NU5nT3c9PSIsInZhbHVlIjoiWFphNTcvZERRSjNkS3FwV3RTRkxFcUV6VHJNZDA4eTdNanREMFNQYXZsc1YyMFFTdEFNUVBKTldJaVlKK3dyZTdyYzN0cisvZldoMmNPenFnbXByTDVlaEI3VXlKa2E1VzBHbzZKNU9IOU00Tzg0ZzBvOHhBM0d6MlRSZEdEcUZTK3Y2M3NsRjJ4ZEVuY1VzQmlpalBnPT0iLCJtYWMiOiI2OWM5ZmM3MDEyNTI4YTE2N2VhMjcxYzI0NmE5YjU2Zjg1MWE5ZjcxZDE4ZTFlNWYyZGE5ZjUyYzQ4NmQ5M2ZmIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InhsSFpKMHgwOTJLNnJyajJSbWdsNFE9PSIsInZhbHVlIjoic2VKRW5md3pxSEpaRlN1T2RKWUJLNWVGbDFZVWN2SmlQajRGVkROZ3ZzLzdiV0g4cWh4U0hYd3NUaGQ5aXFob2hKQlZzVGJsdkRESW1wSzd2WDRFVFRaNUpHdGdkSXFYN1c5OXV5S21veitBelBoM2FLTENDQXB1b2k0Q0djL08iLCJtYWMiOiJmOTQ0YTllNmU2M2E1ZTk0NDI2YjA4OGU4YTM3M2MwYzA3OTNlM2YyZTIxM2IzMjcxMTBkNjBiZDE1ZjAxYTdlIiwidGFnIjoiIn0%3D; apimio_local_session=eyJpdiI6IlovcHY5bndiaktkSS95WFg1UnU0TVE9PSIsInZhbHVlIjoiSTBtRkNjRC9aVzF1eUpDVzFDd1I3cjJMNE9selJIWm5lSFZjNGJxbU9mMktIOHJnb3BHTG1HY1QvdXFNeWpTT1Axei91Z0lRY3l2Q0pWWlFxa0pNM0Jjc0NpdzJjTGF2b0o2eUg4SFJVZGhwZXgxeDRoZDhJdmg4R250ay9ZVjUiLCJtYWMiOiIwMWFlOWE0ZGFiOTkwMDViYzIzMjc5MWIzNGE0ZWYyMDQ3MTczNmNlMGFhYzkxY2JiYzY1MjQyOGZmNjg1YTMxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"63 characters\">1|QRtdjvk3r2KD6Sq63FZBSWVCBmzsXTPvhwN1XrFHsg0VmpaG2uGCyLItysDk|</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ikPoMer4OtoDULeT9sBt2cHa7xhxWBWDS3oAnx7H</span>\"\n  \"<span class=sf-dump-key>apimio_local_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SpQkFOIEOJLpae86rFzEvQ4jqZkdfqtcgZefgT1H</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-156407949 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 04 Jul 2025 09:20:16 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-limit</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">60</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-remaining</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">57</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InJSdjB6aG9YYWVXUDBHeHBPdFYyTXc9PSIsInZhbHVlIjoiZ0llRkRnSmNwU3JqUFVRQS9aWmREMktueVVabzRZbzZ4L3pTd2MrRTlTb2ZsMi94empwK2svSCtRdm56VmU3OVZDVXQyVzVhZ1N0Z05Jdy9YOC9DcllmdFpzeXV2dCt5N2JOZzdPUXVqU2pMNGRVZ3ZJUldsVEUyTjhud1ZjYnQiLCJtYWMiOiJlYjcyMGFjYmRjZDYxNWMwM2JhNTg1ZDgxNTBkNGRiZDdhMjY5ZmNkMTk0MGRmZTU3ZmVlMGQ5ODk4N2NlNjYxIiwidGFnIjoiIn0%3D; expires=Fri, 04 Jul 2025 11:20:16 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"448 characters\">apimio_local_session=eyJpdiI6IlJ6UnJsclhmM3U4cm9PaEJSWFJTRUE9PSIsInZhbHVlIjoieDdSMHlZbk1QV3ZoS0gzYTd1SWkrWnRoaXFRajZKMkJ2ckxEeUR4Y3krcGVodXhrZllyMU42dk5Ydy9tQjkvbGlBRktVdTNqcXJKRWRCRUtkWE0rMy9mNWJMamorWDdmUlVXK2x3ZDlxeDlOamhxWmtEaGdubkhTcEhLYnBMUTciLCJtYWMiOiI2ZDVhMjYwNDU4ZDYwZWJkMTc1NmIxZTFjOGQxYWIwOGU5M2RjYjI2YThhNDkxMWQ5MmYwOTUxYjU2M2JmOTVhIiwidGFnIjoiIn0%3D; expires=Fri, 04 Jul 2025 11:20:16 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InJSdjB6aG9YYWVXUDBHeHBPdFYyTXc9PSIsInZhbHVlIjoiZ0llRkRnSmNwU3JqUFVRQS9aWmREMktueVVabzRZbzZ4L3pTd2MrRTlTb2ZsMi94empwK2svSCtRdm56VmU3OVZDVXQyVzVhZ1N0Z05Jdy9YOC9DcllmdFpzeXV2dCt5N2JOZzdPUXVqU2pMNGRVZ3ZJUldsVEUyTjhud1ZjYnQiLCJtYWMiOiJlYjcyMGFjYmRjZDYxNWMwM2JhNTg1ZDgxNTBkNGRiZDdhMjY5ZmNkMTk0MGRmZTU3ZmVlMGQ5ODk4N2NlNjYxIiwidGFnIjoiIn0%3D; expires=Fri, 04-Jul-2025 11:20:16 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"420 characters\">apimio_local_session=eyJpdiI6IlJ6UnJsclhmM3U4cm9PaEJSWFJTRUE9PSIsInZhbHVlIjoieDdSMHlZbk1QV3ZoS0gzYTd1SWkrWnRoaXFRajZKMkJ2ckxEeUR4Y3krcGVodXhrZllyMU42dk5Ydy9tQjkvbGlBRktVdTNqcXJKRWRCRUtkWE0rMy9mNWJMamorWDdmUlVXK2x3ZDlxeDlOamhxWmtEaGdubkhTcEhLYnBMUTciLCJtYWMiOiI2ZDVhMjYwNDU4ZDYwZWJkMTc1NmIxZTFjOGQxYWIwOGU5M2RjYjI2YThhNDkxMWQ5MmYwOTUxYjU2M2JmOTVhIiwidGFnIjoiIn0%3D; expires=Fri, 04-Jul-2025 11:20:16 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-156407949\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ikPoMer4OtoDULeT9sBt2cHa7xhxWBWDS3oAnx7H</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"32 characters\">http://localhost:8000/onboarding</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>password_hash_web</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"41 characters\">http://localhost:8000/organization/select</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>organization_id</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/api/2024-12/organization", "action_name": "organization.store", "controller_action": "App\\Http\\Controllers\\Api\\OrganizationController@store"}, "badge": null}}