import React, { useState, useEffect } from "react";
import { Upload, Card, Typography, Button, Row, Col, message } from "antd";
import { UploadOutlined, DownloadOutlined } from "@ant-design/icons";
import CSVPreview from "./CSVPreview";
import <PERSON> from "papaparse";
import { router } from "@inertiajs/react";
import { get } from "../../../axios";
import File from "../../../../../public/v2/icons/file.svg";
import CSVFile from "../../../../../public/v2/icons/CSVFile.svg";
import ProductsLayout from "../ProductsLayout.jsx";

const { Dragger } = Upload;
const { Text } = Typography;

const CSVImport = () => {
    const [csvData, setCsvData] = useState(null);
    const [csvFirstFiveRows, setCsvFirstFiveRows] = useState(null);
    const [fileList, setFileList] = useState([]);
    const [formData] = useState(new FormData());
    const [hasExistingProducts, setHasExistingProducts] = useState(false);
    const [loadingProductCount, setLoadingProductCount] = useState(true);

    // Fetch product count on component mount
    useEffect(() => {
        const fetchProductCount = async () => {
            try {
                setLoadingProductCount(true);
                const response = await get('dashboard');
                const productCount = response?.data?.product_count || 0;
                setHasExistingProducts(productCount > 0);
            } catch (error) {
                console.error('Error fetching product count:', error);
                // Default to false if there's an error
                setHasExistingProducts(false);
            } finally {
                setLoadingProductCount(false);
            }
        };

        fetchProductCount();
    }, []);

    const uploadProps = {
        name: "file",
        multiple: false,
        accept: ".csv",
        fileList,
        beforeUpload: (file) => {
            console.log("file");
            // Validate that the file is CSV
            const isCsv = file.type === "text/csv" || file.name.endsWith(".csv");
            if (!isCsv) {
                message.error("You can only upload CSV files!");
                return Upload.LIST_IGNORE;
            }

            // Send the raw file to the backend route in parallel using FormData
            formData.append("file", file);

            // Continue with reading the file for CSV preview using PapaParse
            Papa.parse(file, {
                header: true,           // gives you objects keyed by column name (headers)
                skipEmptyLines: true,   // ignore completely blank lines
                complete: ({ data, meta }) => {
                    console.log("Headers:", meta.fields); // This is equivalent to your `headers` array
                    console.log("Parsed Data:", data);    // This is your rows

                    if (!meta.fields || meta.fields.length < 2) {
                        message.error("Invalid CSV format. File must contain at least two columns.");
                        return;
                    }

                    // Create firstFiveRows in the format expected by backend:
                    // - First row should be headers
                    // - Next rows should be data values as arrays
                    const firstFiveRows = [
                        meta.fields, // Headers as first row
                        ...data.slice(0, 5).map((row) =>
                            meta.fields.map((header) => (row[header] || "").toString().trim())
                        )
                    ];

                    // Set the parsed rows and headers for backend
                    setCsvFirstFiveRows(firstFiveRows);

                    // Set CSV data for preview component with total row count
                    setCsvData({
                        headers: meta.fields,  // Use the headers from PapaParse
                        rows: data.slice(0, 5),  // Only first 5 rows for preview
                        totalRows: data.length   // Total number of rows in the CSV
                    });

                    // You can log it here to check the structure
                    console.log("First Five Rows for Backend:", firstFiveRows);
                    console.log("Total Rows in CSV:", data.length);
                },
                error: (err) => {
                    console.error(err);
                    message.error("Failed to parse CSV file");
                },
            });

            // Prevent default upload behavior (no need to manually handle file reading)
            return false;
        },
        onChange(info) {
            const { status } = info.file;
            if (status === "removed") {
                setFileList([]);
                setCsvData(null);
                return;
            }
            setFileList([info.file]);
        },
        onDrop(e) {
            const file = e.dataTransfer.files[0];
            if (file) {
                setFileList([
                    {
                        uid: "-1",
                        name: file.name,
                        status: "uploading",
                        size: file.size,
                        type: file.type,
                    },
                ]);
            }
        },
    };
    const handleNext = (importAction) => {
        console.log("CSV Data:", csvFirstFiveRows);
        console.log("Import Action:", importAction);
        if (csvFirstFiveRows) {
            formData.append("first_five_rows", JSON.stringify(csvFirstFiveRows));
        }
        // Add the import action to the form data with the key Import_Action
        formData.append("import_action", importAction);
        router.post("/products/import/step2", formData, {
            onSuccess: () => {
                message.success("File successfully uploaded to backend");
                // Show next component on success
            },
            onError: () => {
                message.error("Failed to send file to backend");
            },
        });
    };

    const handleDownloadSample = () => {
        const link = document.createElement("a");
        link.href = "/csv_template/apimio_default.csv";
        link.download = "sample_products.csv";
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    if (csvData) {
        return (
            <div>
                <CSVPreview
                    csvData={csvData}
                    onReupload={() => {
                        setCsvData(null);
                        setFileList([]);
                    }}
                    onNext={handleNext}
                    hasExistingProducts={hasExistingProducts}
                />
            </div>
        );
    }

    return (
        <ProductsLayout activeTab="importcsv">
            <>
        <div className="h-full p-0 m-0">
            <Row gutter={[20, 20]} className="pb-[20px]">
                <Col span={12}>
                    <div>
                        <p className="text-[#252525] font-[600] text-[18px]">Import CSV</p>
                        <p className="text-[#626262] font-normal text-[14px]">Import CSV of all your products</p>
                    </div>
                </Col>
            </Row>
            <div className={`bg-white min-h-screen rounded-[12px] border border-[#DBDBDB]   pb-0 mb-0`}>
                <div className="mx-15">
                    <Dragger {...uploadProps} className="p-6 mb-8">
                        <p className="text-4xl mb-4">
                            <UploadOutlined className="text-purple-600" />
                        </p>
                        <p className="text-gray-700 text-lg">Drag & drop your file here</p>
                        <p className="text-gray-500 mb-2">or</p>
                        <Button type="link" icon={<UploadOutlined />}>
                            Browse Files
                        </Button>
                    </Dragger>

                    <div className="grid md:grid-cols-2 gap-6">
                        <Card className="bg-white border-purple-100 hover:border-purple-200 transition-colors" size="small">
                            <div className="flex justify-between items-center space-x-4">
                                <div className="p-4 bg-[#F1E6F5] rounded-full">
                                    <img src={File} />
                                </div>
                                <div>
                                    <Text strong className="block mb-1">
                                        How to prepare your spreadsheet
                                    </Text>
                                    <Text className="text-xs text-gray-600">
                                        Your CSV should include columns for Product Name, SKU, Price, and Quantity. Make sure all required
                                        fields are filled.
                                    </Text>
                                </div>
                            </div>
                        </Card>

                        <Card className="bg-white border-purple-100 hover:border-purple-200 transition-colors" size="small">
                            <div className="flex justify-between items-center space-x-4">
                                <div className="p-4 bg-[#F1E6F5] rounded-full">
                                    <img src={CSVFile} />
                                </div>
                                <div>
                                    <Text strong className="block mb-1">
                                        Download a Sample CSV
                                    </Text>
                                    <Text className="text-sm text-gray-600">Try it out using a sample csv file to see how it works</Text>
                                </div>
                                <Button
                                    type="link"
                                    icon={<DownloadOutlined />}
                                    className="text-blue-500 hover:text-blue-600"
                                    aria-label="Download sample CSV"
                                    onClick={handleDownloadSample}
                                />
                            </div>
                        </Card>
                    </div>
                </div>
            </div>
        </div>
            </>
        </ProductsLayout>
    );
};

export default CSVImport;
