@layer base, components, utilities;

@import "tailwindcss" layer(utilities);

@source "./resources/**/*.blade.php";
@source "./resources/**/*.jsx";
@source "./resources/**/*.js";
@source "./resources/js/v2/**/*.jsx";
@source "./packages/apimio/mapping-fields-package/resources/js/**/*.{js,jsx}";
@source "./packages/**/resources/**/*.{js,jsx,blade.php}";
@source "./vendor/apimio/mapping-connector-package/resources/js/**/*.{js,jsx}";
@source "./vendor/**/resources/**/*.{js,jsx,blade.php}";

@theme {
  --breakpoint-md2: 1000px;
  --breakpoint-2xl2: 1920px;
}

/* Your other global styles */

@font-face {
    font-family: "Proxima Nova"; /* Capitalize for better readability */
    src: url("/v2/fonts/Proxima_Nova_Regular.otf") format("opentype");
    font-weight: 400; /* Normal */
    font-style: normal;
}
@font-face {
    font-family: "Proxima Nova"; /* Capitalize for better readability */
    src: url("/v2/fonts/Proxima_Nova_Light.otf") format("opentype");
    font-weight: 300; /* Light */
    font-style: normal;
}
@font-face {
    font-family: "Proxima Nova"; /* Capitalize for better readability */
    src: url("/v2/fonts/Proxima_Nova_Semibold.otf") format("opentype");
    font-weight: 600; /* Normal */
    font-style: normal;
}
@font-face {
    font-family: "Proxima Nova"; /* Capitalize for better readability */
    src: url("/v2/fonts/Proxima_Nova_Bold.otf") format("opentype");
    font-weight: 700; /* Normal */
    font-style: normal;
}

body,
div,
h1,
h2,
h3,
h4,
h5,
h6,
p,
input,
label,
span {
    font-family: "Proxima Nova" !important;
}

/* Fix for weight selector background color */
.weight-select-custom .ant-select-selector {
    background-color: #f9fafb !important;
    border-radius: 0px !important;
    border-right: none !important;
}

/* Remove blue border from Ant Design pagination buttons */
.ant-pagination .ant-pagination-item,
.ant-pagination .ant-pagination-item-active,
.ant-pagination .ant-pagination-item:focus,
.ant-pagination .ant-pagination-item:hover,
.ant-pagination-item-link,
.ant-pagination-item button,
.pagination-no-focus-border .ant-btn:focus,
.pagination-no-focus-border .ant-btn:active,
.pagination-no-focus-border .ant-pagination-item-link:focus,
.pagination-no-focus-border button:focus {
    outline: none !important;
    box-shadow: none !important;
}

.ant-pagination-item a:focus,
.ant-pagination-item-link:focus,
.ant-pagination .ant-pagination-item:focus-visible {
    outline: none !important;
    box-shadow: none !important;
    border-color: inherit !important;
}

/* Ensure selected pagination button maintains its correct border color */
.ant-pagination-item-active {
    border: none !important;
}

/* Tailwind CSS specificity fixes for CSV Mapping components */
@layer utilities {
  /* Ensure Tailwind border utilities override Ant Design defaults */
  .border-green-500 {
      border-color: #10b981 !important;
  }

  .border-yellow-500 {
      border-color: #f59e0b !important;
  }

  .border-l-8 {
      border-left-width: 8px !important;
  }

  .border-t {
      border-top-width: 1px !important;
  }

  .border-r {
      border-right-width: 1px !important;
  }

  .border-b {
      border-bottom-width: 1px !important;
  }

  .border {
      border-width: 1px !important;
  }

  .border-2 {
      border-width: 2px !important;
  }
}

@layer utilities {
  /* Ensure Tailwind spacing utilities work properly */
  .px-4 {
      padding-left: 1rem !important;
      padding-right: 1rem !important;
  }

  .py-3 {
      padding-top: 0.75rem !important;
      padding-bottom: 0.75rem !important;
  }

  .mt-2 {
      margin-top: 0.5rem !important;
  }

  .mb-0 {
      margin-bottom: 0 !important;
  }

  /* Ensure Tailwind flexbox utilities work */
  .flex {
      display: flex !important;
  }

  .flex-col {
      flex-direction: column !important;
  }

  .flex-1 {
      flex: 1 1 0% !important;
  }

  .justify-start {
      justify-content: flex-start !important;
  }

  .justify-between {
      justify-content: space-between !important;
  }

  .items-center {
      align-items: center !important;
  }

  .gap-1 {
      gap: 0.25rem !important;
  }

  .gap-6 {
      gap: 1.5rem !important;
  }

  /* Ensure Tailwind positioning utilities work */
  .absolute {
      position: absolute !important;
  }

  .top-2 {
      top: 0.5rem !important;
  }

  .right-2 {
      right: 0.5rem !important;
  }

  .z-50 {
      z-index: 50 !important;
  }

  /* Ensure Tailwind border radius works */
  .rounded-md {
      border-radius: 0.375rem !important;
  }

  /* Ensure Tailwind shadow utilities work */
  .shadow-md {
      box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1) !important;
  }

  /* Ensure Tailwind text utilities work */
  .text-xs {
      font-size: 0.75rem !important;
      line-height: 1rem !important;
  }

  .text-yellow-500 {
      color: #f59e0b !important;
  }
}
