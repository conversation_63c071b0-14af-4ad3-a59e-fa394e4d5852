@tailwind base;
@tailwind components;
@tailwind utilities;
@import "tailwindcss";

/* Your other global styles */

@font-face {
    font-family: "Proxima Nova"; /* Capitalize for better readability */
    src: url("/v2/fonts/Proxima_Nova_Regular.otf") format("opentype");
    font-weight: 400; /* Normal */
    font-style: normal;
}
@font-face {
    font-family: "Proxima Nova"; /* Capitalize for better readability */
    src: url("/v2/fonts/Proxima_Nova_Light.otf") format("opentype");
    font-weight: 300; /* Light */
    font-style: normal;
}
@font-face {
    font-family: "Proxima Nova"; /* Capitalize for better readability */
    src: url("/v2/fonts/Proxima_Nova_Semibold.otf") format("opentype");
    font-weight: 600; /* Normal */
    font-style: normal;
}
@font-face {
    font-family: "Proxima Nova"; /* Capitalize for better readability */
    src: url("/v2/fonts/Proxima_Nova_Bold.otf") format("opentype");
    font-weight: 700; /* Normal */
    font-style: normal;
}

body,
div,
h1,
h2,
h3,
h4,
h5,
h6,
p,
input,
label,
span {
    font-family: "Proxima Nova" !important;
}

/* Fix for weight selector background color */
.weight-select-custom .ant-select-selector {
    background-color: #f9fafb !important;
    border-radius: 0px !important;
    border-right: none !important;
}

/* Remove blue border from Ant Design pagination buttons */
.ant-pagination .ant-pagination-item,
.ant-pagination .ant-pagination-item-active,
.ant-pagination .ant-pagination-item:focus,
.ant-pagination .ant-pagination-item:hover,
.ant-pagination-item-link,
.ant-pagination-item button,
.pagination-no-focus-border .ant-btn:focus,
.pagination-no-focus-border .ant-btn:active,
.pagination-no-focus-border .ant-pagination-item-link:focus,
.pagination-no-focus-border button:focus {
    outline: none !important;
    box-shadow: none !important;
}

.ant-pagination-item a:focus,
.ant-pagination-item-link:focus,
.ant-pagination .ant-pagination-item:focus-visible {
    outline: none !important;
    box-shadow: none !important;
    border-color: inherit !important;
}

/* Ensure selected pagination button maintains its correct border color */
.ant-pagination-item-active {
    border: none !important;
}
