{"__meta": {"id": "01JZABQHVT2N70YFGF67ST5RMA", "datetime": "2025-07-04 09:19:25", "utime": **********.563589, "method": "GET", "uri": "/api/2024-12/organization", "ip": "127.0.0.1"}, "php": {"version": "8.2.4", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751620764.803448, "end": **********.56361, "duration": 0.7601621150970459, "duration_str": "760ms", "measures": [{"label": "Booting", "start": 1751620764.803448, "relative_start": 0, "end": **********.431902, "relative_end": **********.431902, "duration": 0.****************, "duration_str": "628ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.431929, "relative_start": 0.****************, "end": **********.563612, "relative_end": 1.9073486328125e-06, "duration": 0.****************, "duration_str": "132ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.451196, "relative_start": 0.****************, "end": **********.459356, "relative_end": **********.459356, "duration": 0.008160114288330078, "duration_str": "8.16ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.557298, "relative_start": 0.***************, "end": **********.557548, "relative_end": **********.557548, "duration": 0.00025010108947753906, "duration_str": "250μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.559775, "relative_start": 0.****************, "end": **********.559896, "relative_end": **********.559896, "duration": 0.00012087821960449219, "duration_str": "121μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "30MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "route": {"uri": "GET api/2024-12/organization", "middleware": "api, auth:sanctum", "as": "organization.index", "controller": "App\\Http\\Controllers\\Api\\OrganizationController@index<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FOrganizationController.php&line=28\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers", "prefix": "api/2024-12", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FOrganizationController.php&line=28\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Api/OrganizationController.php:28-39</a>"}, "queries": {"count": 2, "nb_statements": 2, "nb_visible_statements": 2, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.02958, "accumulated_duration_str": "29.58ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": null, "name": "vendor/laravel/sanctum/src/Http/Middleware/AuthenticateSession.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\AuthenticateSession.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.484622, "duration": 0.02831, "duration_str": "28.31ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 0, "width_percent": 95.707}, {"sql": "select `organizations`.*, `organization_user`.`user_id` as `pivot_user_id`, `organization_user`.`organization_id` as `pivot_organization_id` from `organizations` inner join `organization_user` on `organizations`.`id` = `organization_user`.`organization_id` where `organization_user`.`user_id` = 1 and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) order by `updated_at` desc", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 37}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.545374, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "OrganizationController.php:37", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FOrganizationController.php&line=37", "ajax": false, "filename": "OrganizationController.php", "line": "37"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 95.707, "width_percent": 4.293}]}, "models": {"data": {"App\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "ikPoMer4OtoDULeT9sBt2cHa7xhxWBWDS3oAnx7H", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/api/2024-12/organization\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "password_hash_web": "null", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/organization/select\"\n]"}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/api/2024-12/organization", "action_name": "organization.index", "controller_action": "App\\Http\\Controllers\\Api\\OrganizationController@index", "uri": "GET api/2024-12/organization", "controller": "App\\Http\\Controllers\\Api\\OrganizationController@index<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FOrganizationController.php&line=28\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers", "prefix": "api/2024-12", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FOrganizationController.php&line=28\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Api/OrganizationController.php:28-39</a>", "middleware": "api", "telescope": "<a href=\"http://localhost:8000/_debugbar/telescope/9f4f1959-f459-4a10-9498-330c463b8ac5\" target=\"_blank\">View in Telescope</a>", "duration": "762ms", "peak_memory": "32MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-533918933 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-533918933\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-872024591 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-872024591\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-101026428 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">Bearer null******</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IjhLWGFtMFFQOVpDeFFoRjRReUJsZlE9PSIsInZhbHVlIjoiOEFqSEFNZDJubVQrMXk4SGg3WjE1ZGg2VHlWdjBMYXlQcmUvTjg4enR2S21KcXZCUGRRcU9WbVpYdkFUdnZPa25WWE4vS1Axc2JBRFZjUmVEb0hDcm1Zem5mTDdNeUR2V1RIWHpacmZGNm5sS3J6SnJaY2FWYlNiVndYZTJDRDAiLCJtYWMiOiIxNTE2OGQ2YjA2MTc3NzM4ZTJhNjc5N2YxNGZiZTQ4ZmFlMDk5Y2FmOGQ2NjE2YzUyMWIwNTU1ZGQzMmY5NWQ4IiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost:8000/organization</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1205 characters\">__stripe_mid=5fa877f3-5d7b-4255-b2c2-ced2f9ae1950465a52; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Ikh6c1lPTHd2QXUrT2F0OWs5NU5nT3c9PSIsInZhbHVlIjoiWFphNTcvZERRSjNkS3FwV3RTRkxFcUV6VHJNZDA4eTdNanREMFNQYXZsc1YyMFFTdEFNUVBKTldJaVlKK3dyZTdyYzN0cisvZldoMmNPenFnbXByTDVlaEI3VXlKa2E1VzBHbzZKNU9IOU00Tzg0ZzBvOHhBM0d6MlRSZEdEcUZTK3Y2M3NsRjJ4ZEVuY1VzQmlpalBnPT0iLCJtYWMiOiI2OWM5ZmM3MDEyNTI4YTE2N2VhMjcxYzI0NmE5YjU2Zjg1MWE5ZjcxZDE4ZTFlNWYyZGE5ZjUyYzQ4NmQ5M2ZmIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjhLWGFtMFFQOVpDeFFoRjRReUJsZlE9PSIsInZhbHVlIjoiOEFqSEFNZDJubVQrMXk4SGg3WjE1ZGg2VHlWdjBMYXlQcmUvTjg4enR2S21KcXZCUGRRcU9WbVpYdkFUdnZPa25WWE4vS1Axc2JBRFZjUmVEb0hDcm1Zem5mTDdNeUR2V1RIWHpacmZGNm5sS3J6SnJaY2FWYlNiVndYZTJDRDAiLCJtYWMiOiIxNTE2OGQ2YjA2MTc3NzM4ZTJhNjc5N2YxNGZiZTQ4ZmFlMDk5Y2FmOGQ2NjE2YzUyMWIwNTU1ZGQzMmY5NWQ4IiwidGFnIjoiIn0%3D; apimio_local_session=eyJpdiI6ImFhbkVPL0MwVHNkbDZmVGQyL0ZzOUE9PSIsInZhbHVlIjoiQ2s1R1ltRkZkQnNaUlIyVEt6QVo5c1BWdlovTnlNaHJCc2U2eUZOQ3BaRHZxV2gra1hKbU9aRkhESVBzRi92WmhnODJ4bGxCTlJtWTU4aTBNM2RXdHpsTTQ2TnRRb0QrSk1vUm03UFVNeG5VWVBRc0RJNk9Bd1FiTTZqeWdJN0UiLCJtYWMiOiJlZjZjY2E4YmRmZGI5ODZkMjAzNTg0ZTBiMDg0OWE5ZTU3MGMzNjQxY2Q0NTIwMDgwNzY3YzVmOWJhMjFjZGFiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-101026428\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1541195169 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"63 characters\">1|QRtdjvk3r2KD6Sq63FZBSWVCBmzsXTPvhwN1XrFHsg0VmpaG2uGCyLItysDk|</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ikPoMer4OtoDULeT9sBt2cHa7xhxWBWDS3oAnx7H</span>\"\n  \"<span class=sf-dump-key>apimio_local_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SpQkFOIEOJLpae86rFzEvQ4jqZkdfqtcgZefgT1H</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1541195169\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2035107491 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 04 Jul 2025 09:19:25 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-limit</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">60</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-remaining</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">58</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Im0zZThoRzBPMkRFbkxpbnMrNU5Ccnc9PSIsInZhbHVlIjoib0ZOWS82TG56UzFMV2txeHBmWTlDZDlTWlZHbCtjanJDVkhRSXhEZkN2RVRVMDdjdkE3YVlvdDJkTndkaC9kV01ZYTAzKzRMSEYzYmxPR08rUzB2Z1dmRENBNXJQZWVGVC9DWUsvby9jd3VzVGpSQ3NNOEg0UVNlTWZVemFnSW8iLCJtYWMiOiI3NzBjOThhYzYwOTdkYTdjNTliMDJjZDVkYmNlYTQ2NGI4NGJjZjljODVjNTYzZWM3Y2Q2NmU2M2NiMzI5Y2RhIiwidGFnIjoiIn0%3D; expires=Fri, 04 Jul 2025 11:19:25 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"448 characters\">apimio_local_session=eyJpdiI6Imdjd1ZJclVObFFBOGkyUHNZVmJZYWc9PSIsInZhbHVlIjoidGtMSkpQSEpYVEJCZ0ZuSnJPWnV4am4walFPZk9LY1NOOEduTFhzdVh4eUhzZU5NSC92RGUrYmM0VnVuZU1EOUVYY2JmcTRkaEJLZStaamRlQ0VEeFBqU213cGFTYWl3RnZrZDNKZ3FkY0VIbWVCSDA4RVpZQ1NmdU9yekpCNm4iLCJtYWMiOiI0OTYwNDkzOWY4NTYxYWMxNjVhNDdjNzVhMGJkZWE1M2ZiYzU1ZjYwYWY5MjQ2ZTVkMGI2ZDk5MjQ2ZTMyZmQ3IiwidGFnIjoiIn0%3D; expires=Fri, 04 Jul 2025 11:19:25 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Im0zZThoRzBPMkRFbkxpbnMrNU5Ccnc9PSIsInZhbHVlIjoib0ZOWS82TG56UzFMV2txeHBmWTlDZDlTWlZHbCtjanJDVkhRSXhEZkN2RVRVMDdjdkE3YVlvdDJkTndkaC9kV01ZYTAzKzRMSEYzYmxPR08rUzB2Z1dmRENBNXJQZWVGVC9DWUsvby9jd3VzVGpSQ3NNOEg0UVNlTWZVemFnSW8iLCJtYWMiOiI3NzBjOThhYzYwOTdkYTdjNTliMDJjZDVkYmNlYTQ2NGI4NGJjZjljODVjNTYzZWM3Y2Q2NmU2M2NiMzI5Y2RhIiwidGFnIjoiIn0%3D; expires=Fri, 04-Jul-2025 11:19:25 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"420 characters\">apimio_local_session=eyJpdiI6Imdjd1ZJclVObFFBOGkyUHNZVmJZYWc9PSIsInZhbHVlIjoidGtMSkpQSEpYVEJCZ0ZuSnJPWnV4am4walFPZk9LY1NOOEduTFhzdVh4eUhzZU5NSC92RGUrYmM0VnVuZU1EOUVYY2JmcTRkaEJLZStaamRlQ0VEeFBqU213cGFTYWl3RnZrZDNKZ3FkY0VIbWVCSDA4RVpZQ1NmdU9yekpCNm4iLCJtYWMiOiI0OTYwNDkzOWY4NTYxYWMxNjVhNDdjNzVhMGJkZWE1M2ZiYzU1ZjYwYWY5MjQ2ZTVkMGI2ZDk5MjQ2ZTMyZmQ3IiwidGFnIjoiIn0%3D; expires=Fri, 04-Jul-2025 11:19:25 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2035107491\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1943230417 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ikPoMer4OtoDULeT9sBt2cHa7xhxWBWDS3oAnx7H</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"46 characters\">http://localhost:8000/api/2024-12/organization</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>password_hash_web</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"41 characters\">http://localhost:8000/organization/select</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1943230417\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/api/2024-12/organization", "action_name": "organization.index", "controller_action": "App\\Http\\Controllers\\Api\\OrganizationController@index"}, "badge": null}}